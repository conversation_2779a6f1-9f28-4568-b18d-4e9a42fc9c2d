// types/support.ts

// Structure for a single support request from the API
export interface SupportRequest {
  id: number;
  member_id: string;
  first_name: string;
  last_name: string;
  attachment: string | null;
  subject: string;
  message: string;
  response: string;
  Status: number;
  status: string;
  created_at: string;
}

// Structure for the API response when fetching all support requests
export interface SupportRequestsApiResponse {
  message: string;
  result: SupportRequest[];
  status: number;
}

// Structure for the API response when fetching available status options
export interface SupportStatusApiResponse {
  message: string;
  result: string[];
  status: number;
}

// Structure for the API response when updating a support request
export interface UpdateSupportRequestApiResponse {
  message: string;
  result: string;
  status: number;
}

// Parameters for fetching support requests
export interface FetchSupportRequestsParams {
  status?: string;
  start?: string;
  end?: string;
  location_id?: number;
}

// Payload for updating a support request
export interface UpdateSupportRequestPayload {
  response: string;
  status: string;
}

// Transformed support request for UI
export interface SupportTicket {
  id: string;
  ticketNumber: string;
  date: string;
  status: string;
  title: string;
  description: string;
  response: string;
  attachment: string | null;
  user: {
    id: string;
    name: string;
    email: string;
    ticketCount: number;
  };
  isActive: boolean;
  created_at: string;
}
