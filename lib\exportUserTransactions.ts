"use server";

import { cookies } from "next/headers";

export async function exportUserTransactions(memberId: string) {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found");
  }

  if (!memberId) {
    throw new Error("Member ID is required");
  }

  const response = await fetch(
    `${BASE_URL}/admin/transactions/all?member_id=${memberId}&export=true`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to export user transactions");
  }

  const blob = await response.blob();
  return blob;
}
