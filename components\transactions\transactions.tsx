/* eslint-disable @typescript-eslint/no-unused-vars */
// components/transactions/transactions.tsx (Client Component)
"use client";

import React, { useEffect, useMemo, useState } from "react";
import { useBranch } from "@/store/branch-provider";
import { Card } from "@/components/ui/card";
import { format, isValid } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { fetchTransactions } from "@/lib/fetchTransactions";
import { toast } from "sonner";
import { exportTransactions } from "@/lib/exportTransactions";

import TransactionHeader from "./TransactionHeader";
import FilterPopover from "./FilterPopover";
import ActiveFiltersWrapper from "./ActiveFiltersWrapper";
import TransactionTable from "./TransactionTable";
import TransactionTableSkeleton from "./TransactionTableSkeleton";
import TransactionTableEmpty from "./TransactionTableEmpty";
import TransactionPagination from "./TransactionPagination";
import TransactionSummary from "./TransactionSummary";
import VerifyPaymentDialog from "./VerifyPaymentDialog";
import { Table, TableBody } from "../ui/table";
import { FetchTransactionsParams, Transaction } from "@/types/transaction";

interface TransactionsProps {
  initialSearch?: string;
}

// Helper function to parse date string (DD/MM/YYYY) from UI data
function parseUIDate(dateStr: string): Date | null {
  try {
    const [day, month, year] = dateStr.split("/").map(Number);
    const date = new Date(year, month - 1, day);
    return isValid(date) ? date : null;
  } catch {
    return null;
  }
}

// Define the type for the date range state
type DateRangeType = { from: Date | null; to: Date | null };

export default function Transactions({
  initialSearch = "",
}: TransactionsProps) {
  const itemsPerPage = 10;
  const { selectedBranchId } = useBranch();

  const [page, setPage] = useState(1);
  const [search, setSearch] = useState(initialSearch);
  const [dateRange, setDateRange] = useState<DateRangeType>({
    from: null,
    to: null,
  });
  const [priceMin, setPriceMin] = useState<number | null>(null);
  const [priceMax, setPriceMax] = useState<number | null>(null);
  const [localPriceRange, setLocalPriceRange] = useState<[number, number]>([
    0, 10000000,
  ]);
  const [typeFilters, setTypeFilters] = useState<string[]>([]);
  const [sort, setSort] = useState("Date");
  const [selectedCurrency, setSelectedCurrency] = useState<string | null>(null);

  const periodValues = ["day", "week", "month", "year"] as const;
  type PeriodValue = (typeof periodValues)[number];

  // Make sure period starts as null to avoid showing a filter by default
  const [period, setPeriod] = useState<PeriodValue | null>(null);
  const [showVerifyPaymentDialog, setShowVerifyPaymentDialog] = useState(false);

  // Local state for date picker
  const [calendarOpen, setCalendarOpen] = useState(false);

  // --- React Query Data Fetching ---
  const apiFetchParams: FetchTransactionsParams = useMemo(
    () => ({
      start:
        dateRange?.from && isValid(dateRange.from)
          ? format(dateRange.from, "yyyy-MM-dd")
          : undefined,
      end:
        dateRange?.to && isValid(dateRange.to)
          ? format(dateRange.to, "yyyy-MM-dd")
          : undefined,
      price_min: priceMin ?? undefined,
      price_max: priceMax ?? undefined,
      period: period ?? undefined,
      location_id: selectedBranchId !== null ? selectedBranchId : undefined,
      currency: selectedCurrency ?? undefined,
    }),
    [dateRange, priceMin, priceMax, period, selectedBranchId, selectedCurrency]
  );

  const queryKey = ["transactions", apiFetchParams];

  const {
    data: queryResult,
    isLoading: isQueryLoading,
    isFetching: isQueryFetching,
    error: queryError,
  } = useQuery<{ transactions: Transaction[] }>({
    queryKey: queryKey,
    queryFn: () => fetchTransactions(apiFetchParams),
    placeholderData: (previousData) => previousData,
  });

  useEffect(() => {
    if (queryError) {
      toast(`Error loading transactions: ${(queryError as Error).message}`);
      // If there's an error, don't show loading state
      setIsSummaryLoading(false);
    }
  }, [queryError]);

  const activeFilterCount = useMemo(() => {
    let count = 0;
    if (dateRange?.from || dateRange?.to) count++;
    if (typeFilters.length) count++;
    if (search) count++;
    if (priceMin !== null || priceMax !== null) count++;
    if (period) count++;
    if (selectedCurrency) count++;
    return count;
  }, [
    dateRange,
    typeFilters,
    search,
    priceMin,
    priceMax,
    period,
    selectedCurrency,
  ]);

  const filteredTransactions = useMemo(() => {
    const allFetchedTransactions = queryResult?.transactions ?? [];
    return allFetchedTransactions.filter((transaction) => {
      const lowerSearch = search?.toLowerCase() ?? "";
      const matchesSearch =
        !search ||
        transaction.id.toLowerCase().includes(lowerSearch) ||
        transaction.name.toLowerCase().includes(lowerSearch) ||
        transaction.email.toLowerCase().includes(lowerSearch) ||
        transaction.phone.includes(search) ||
        transaction.type.toLowerCase().includes(lowerSearch) ||
        transaction.category.toLowerCase().includes(lowerSearch);

      const matchesType =
        typeFilters.length === 0 || typeFilters.includes(transaction.category);

      // Add currency filter - ensure consistent currency code comparison
      const matchesCurrency =
        !selectedCurrency ||
        (selectedCurrency === "NGN" && transaction.currency === "NGN") ||
        (selectedCurrency === "GBP" && transaction.currency === "GBP") ||
        (selectedCurrency === "EUR" && transaction.currency === "EUR") ||
        (selectedCurrency === "USD" && transaction.currency === "USD");

      return matchesSearch && matchesType && matchesCurrency;
    });
  }, [search, typeFilters, selectedCurrency, queryResult?.transactions]);

  const sortedTransactions = useMemo(() => {
    const transactionsToSort = [...filteredTransactions];
    if (sort === "Date") {
      return transactionsToSort.sort((a, b) => {
        const dateA = parseUIDate(a.date);
        const dateB = parseUIDate(b.date);
        if (!dateA && !dateB) return 0;
        if (!dateA) return 1;
        if (!dateB) return -1;
        return dateB.getTime() - dateA.getTime();
      });
    }
    if (sort === "Amount") {
      return transactionsToSort.sort((a, b) => b.amountValue - a.amountValue);
    }
    if (sort === "Status") {
      return transactionsToSort.sort((a, b) =>
        a.status.localeCompare(b.status)
      );
    }
    if (sort === "Type") {
      return transactionsToSort.sort((a, b) => a.type.localeCompare(b.type));
    }
    return transactionsToSort;
  }, [filteredTransactions, sort]);

  const totalPages = Math.ceil(sortedTransactions.length / itemsPerPage);
  const currentItems = useMemo(() => {
    const adjustedPage = Math.max(1, Math.min(page ?? 1, totalPages || 1));
    if ((page ?? 1) !== adjustedPage && totalPages > 0) {
      setTimeout(() => setPage(adjustedPage), 0);
    }
    const startIndex = (adjustedPage - 1) * itemsPerPage;
    return sortedTransactions.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedTransactions, page, itemsPerPage, totalPages, setPage]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value || "");
    setPage(1);
  };

  const handleLocalPriceRangeChange = (value: number[]) => {
    setLocalPriceRange(value as [number, number]);
  };

  const handlePriceRangeCommit = (value: number[]) => {
    setPriceMin(value[0]);
    setPriceMax(value[1]);
    setPage(1);
  };

  // Status filter has been removed

  const handleTypeFilterChange = (type: string) => {
    setTypeFilters((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
    setPage(1);
  };

  const handleDateRangeChange = (
    range: { from?: Date | undefined; to?: Date | undefined } | undefined
  ) => {
    setDateRange({ from: range?.from ?? null, to: range?.to ?? null });
    setPage(1);
    if (range?.to) {
      setCalendarOpen(false);
    }
  };

  const clearAllFilters = () => {
    setSearch("");
    setPriceMin(null);
    setPriceMax(null);
    setTypeFilters([]);
    setDateRange({ from: null, to: null });
    setPeriod(null);
    setSelectedCurrency(null);
    setPage(1);
    setSort("Date");

    // Reset local state
    setLocalPriceRange([0, 10000000]);
  };

  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    try {
      setIsExporting(true);
      const blob = await exportTransactions();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "transactions.csv");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      toast.success("Transactions exported successfully");
    } catch (error) {
      toast.error("Failed to export transactions");
    } finally {
      setIsExporting(false);
    }
  };

  // We still need getStatusColor for the transaction table
  const getStatusColor = (status: string) => {
    const upperStatus = status?.toUpperCase();
    switch (upperStatus) {
      case "COMPLETED":
      case "SUCCESS":
        return "bg-[#c5ead1] text-[#51ac32]";
      case "PENDING":
        return "bg-[#ffe580] text-[#dca821]";
      case "REFUNDED":
        return "bg-[#e5e4e4] text-[#707070]";
      case "CANCELLED":
      case "FAILED":
        return "bg-[#f7b997] text-[#ef732f]";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const formatDateRange = () => {
    const { from, to } = dateRange ?? { from: null, to: null };
    if (!from && !to) return "Select dates";
    try {
      if (from && to) {
        if (format(from, "yyyy-MM-dd") === format(to, "yyyy-MM-dd")) {
          return format(from, "dd MMM yyyy");
        }
        return `${format(from, "dd MMM")} - ${format(to, "dd MMM yyyy")}`;
      }
      if (from) return `From ${format(from, "dd MMM yyyy")}`;
      if (to) return `Until ${format(to, "dd MMM yyyy")}`;
    } catch (e) {
      console.error("Error formatting date range:", e);
      return "Invalid date range";
    }
    return "Select dates";
  };

  // Separate loading states for table and summary
  const isTableLoading = isQueryLoading || isQueryFetching;

  // For summary, only show loading on initial load, not during filtering
  const [isSummaryLoading, setIsSummaryLoading] = useState(true);

  // Add a timeout to hide the loading state after 3 seconds, regardless of API response
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsSummaryLoading(false);
    }, 3000);

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Stop showing loading state when we've received a response from the API,
    // regardless of whether there's data or not
    if (queryResult?.transactions !== undefined) {
      setIsSummaryLoading(false);
    }
  }, [queryResult?.transactions]);

  return (
    <main className="container mx-auto px-6 py-6">
      {/* Transaction Summary */}
      <TransactionSummary
        transactions={sortedTransactions}
        isLoading={isSummaryLoading}
      />

      <Card className="overflow-hidden">
        <div className="p-6">
          <TransactionHeader
            transactionCount={sortedTransactions.length}
            search={search}
            handleSearch={handleSearch}
            activeFilterCount={activeFilterCount}
            handleExport={handleExport}
            isExportLoading={isExporting}
            onVerifyPayment={() => setShowVerifyPaymentDialog(true)}
          >
            {/* Pass FilterPopover as children to TransactionHeader's Popover */}
            <FilterPopover
              activeFilterCount={activeFilterCount}
              clearAllFilters={clearAllFilters}
              dateRange={dateRange}
              calendarOpen={calendarOpen}
              setCalendarOpen={setCalendarOpen}
              handleDateRangeChange={handleDateRangeChange}
              formatDateRange={formatDateRange}
              typeFilters={typeFilters}
              handleTypeFilterChange={handleTypeFilterChange}
              localPriceRange={localPriceRange}
              handleLocalPriceRangeChange={handleLocalPriceRangeChange}
              handlePriceRangeCommit={handlePriceRangeCommit}
              period={period}
              setPeriod={setPeriod}
              periodValues={periodValues}
              selectedCurrency={selectedCurrency}
              onCurrencyChange={setSelectedCurrency}
            />
          </TransactionHeader>

          <ActiveFiltersWrapper
            activeFilterCount={activeFilterCount}
            dateRange={dateRange}
            formatDateRange={formatDateRange}
            handleDateRangeChange={handleDateRangeChange}
            priceMin={priceMin}
            priceMax={priceMax}
            setPriceMin={setPriceMin}
            setPriceMax={setPriceMax}
            setLocalPriceRange={setLocalPriceRange}
            typeFilters={typeFilters}
            setTypeFilters={setTypeFilters}
            period={period}
            setPeriod={setPeriod}
            selectedCurrency={selectedCurrency}
            onCurrencyChange={setSelectedCurrency}
          />

          {/* Data Table Section Wrapper for Min Height */}
          <div className="min-h-[500px]">
            {/* Show skeleton on initial load or when fetching/updating URL */}
            {isTableLoading ? (
              <TransactionTableSkeleton />
            ) : (
              <div className="grid grid-cols-1 mb-6">
                {currentItems.length > 0 ? (
                  <TransactionTable
                    currentItems={currentItems}
                    getStatusColor={getStatusColor}
                  />
                ) : (
                  // Wrap the empty state in a Table structure
                  <Table>
                    <TableBody>
                      <TransactionTableEmpty
                        activeFilterCount={activeFilterCount}
                        clearAllFilters={clearAllFilters}
                      />
                    </TableBody>
                  </Table>
                )}
                {/* Render pagination only if there are items OR if it's not loading and there are pages */}
                {currentItems.length > 0 && totalPages > 1 && (
                  <TransactionPagination
                    page={page}
                    totalPages={totalPages}
                    setPage={setPage}
                  />
                )}
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Verify Payment Dialog */}
      <VerifyPaymentDialog
        isOpen={showVerifyPaymentDialog}
        onClose={() => setShowVerifyPaymentDialog(false)}
      />
    </main>
  );
}
