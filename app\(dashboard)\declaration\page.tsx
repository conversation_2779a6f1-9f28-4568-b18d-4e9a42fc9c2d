import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { getQueryClient } from "@/store/get-query-client";
import { prefetchDeclarations } from "@/lib/prefetchDeclarations";
import DeclarationPage from "@/components/declarations/declaration";

export default async function Declaration() {
  const queryClient = getQueryClient();

  await prefetchDeclarations();

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <DeclarationPage />
    </HydrationBoundary>
  );
}
