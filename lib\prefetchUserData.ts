"use server";

import { getQueryClient } from "@/store/get-query-client";
import { fetchUserById } from "./fetchUserById";
import { fetchUserWallets } from "./fetchWallets";
import { fetchUserTransactions } from "./fetchUserTransactions";
import { fetchTotalGivingAnalytics } from "./fetchTotalGiving";

export async function prefetchUserData(userId: string) {
  const queryClient = getQueryClient();

  // Define query keys
  const userQueryKey = ["user", userId];
  const walletsQueryKey = ["userWallets", userId];
  const transactionsQueryKey = ["userTransactions", userId];

  // Get current date details for giving analytics prefetch
  const currentDate = new Date();
  const currentMonth = (currentDate.getMonth() + 1).toString().padStart(2, "0");
  const currentYear = currentDate.getFullYear().toString();
  const defaultCurrency = "NGN";

  const givingQueryKey = [
    "userTotalGiving",
    userId,
    currentMonth,
    currentYear,
    defaultCurrency,
  ];

  // Prefetch all data concurrently
  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: userQueryKey,
      queryFn: () => fetchUserById(userId),
    }),
    queryClient.prefetchQuery({
      queryKey: walletsQueryKey,
      queryFn: () => fetchUserWallets(userId),
    }),
    queryClient.prefetchQuery({
      queryKey: transactionsQueryKey,
      queryFn: () => fetchUserTransactions(userId),
    }),
    queryClient.prefetchQuery({
      queryKey: givingQueryKey,
      queryFn: () =>
        fetchTotalGivingAnalytics({
          userId,
          month: currentMonth,
          year: currentYear,
          currency: defaultCurrency,
        }),
    }),
  ]);

  return queryClient;
}
