// lib/fetchCountries.ts
"use server";

import { cookies } from "next/headers";
import type { Country, CountriesApiResponse } from "@/types/country";

const API_BASE_URL = process.env.BASE_URL;

// --- Fetch Countries ---
export async function fetchCountries(): Promise<Country[]> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/countries`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      // For 404 errors, return empty array
      if (response.status === 404) {
        console.warn(
          `API Endpoint not found (404) - ${API_URL} - Using empty array`
        );
        return [];
      }

      // For other errors, try to parse JSON but handle gracefully if it fails
      let errorMessage = "Failed to fetch countries.";
      try {
        // Check if there's content to parse
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          if (
            typeof errorData === "object" &&
            errorData !== null &&
            "message" in errorData &&
            typeof errorData.message === "string"
          ) {
            errorMessage = errorData.message;
          }
          console.error(`API Error Response (${response.status}):`, errorData);
        } else {
          // If not JSON, try to get text content
          const textContent = await response.text();
          console.error(
            `API Error Response (${response.status}):`,
            textContent ? textContent : "No response body"
          );
        }
      } catch (e) {
        console.error("Failed to parse error response:", e);
      }

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: CountriesApiResponse = await response.json();

    if (data.message !== "success" || !data.result) {
      console.error("Unexpected API response structure for countries:", data);
      throw new Error("Failed to fetch countries or invalid data format.");
    }

    // Process the countries to ensure valid flag URLs
    const processedCountries = data.result.map((country) => ({
      ...country,
      // Ensure flag URLs are not empty strings
      flag_png: country.flag_png || null,
      flag_svg: country.flag_svg || null,
    }));

    return processedCountries as Country[];
  } catch (error) {
    console.error("Error fetching or processing countries:", error);
    // Return empty array instead of throwing an error
    return [];
  }
}
