"use client";

import React from "react";
import { Check, ChevronsUpDown } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from "@/components/ui/command";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface CurrencyFilterProps {
  selectedCurrency: string | null;
  onCurrencyChange: (currency: string | null) => void;
}

const currencies = [
  { value: "NGN", label: "Nigerian Naira (₦)" },
  { value: "GBP", label: "British Pound (£)" },
  { value: "EUR", label: "Euro (€)" },
  { value: "USD", label: "US Dollar ($)" },
];

export default function CurrencyFilter({
  selectedCurrency,
  onCurrencyChange,
}: CurrencyFilterProps) {
  const [open, setOpen] = React.useState(false);

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-[180px] justify-between"
        >
          {selectedCurrency
            ? currencies.find((currency) => currency.value === selectedCurrency)
                ?.label
            : "Filter by Currency"}
          <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[180px] p-0">
        <Command>
          <CommandInput placeholder="Search currency..." />
          <CommandEmpty>No currency found.</CommandEmpty>
          <CommandGroup>
            <CommandItem
              onSelect={() => {
                onCurrencyChange(null);
                setOpen(false);
              }}
              className="text-muted-foreground"
            >
              <Check
                className={cn(
                  "mr-2 h-4 w-4",
                  !selectedCurrency ? "opacity-100" : "opacity-0"
                )}
              />
              All Currencies
            </CommandItem>
            {currencies.map((currency) => (
              <CommandItem
                key={currency.value}
                onSelect={() => {
                  onCurrencyChange(currency.value);
                  setOpen(false);
                }}
              >
                <Check
                  className={cn(
                    "mr-2 h-4 w-4",
                    selectedCurrency === currency.value
                      ? "opacity-100"
                      : "opacity-0"
                  )}
                />
                {currency.label}
              </CommandItem>
            ))}
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
}
