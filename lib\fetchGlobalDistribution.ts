// src/app/api/global-distribution/route.ts
"use server";
import { cookies } from "next/headers";
import type {
  ApiUser,
  UserApiResponse,
  GlobalDistributionDataPoint,
  GlobalDistributionApiResponse,
} from "@/types/user";

const countryNameToCode: Record<string, string> = {
  // Common countries
  NIGERIA: "NG",
  "UNITED KINGDOM": "GB",
  "UNITED ARAB EMIRATES": "AE",
  "UNITED STATES": "US",
  USA: "US",
  CANADA: "CA",
  BRAZIL: "BR",
  RUSSIA: "RU",
  CHINA: "CN",
  AUSTRALIA: "AU",

  // Add lowercase versions for direct matching
  nigeria: "NG",
  "united kingdom": "GB",
  "united arab emirates": "AE",
  "united states": "US",
  usa: "US",
  canada: "CA",
  brazil: "BR",
  russia: "RU",
  china: "CN",
  australia: "AU",

  // Add more countries as needed
  ghana: "GH",
  GHANA: "GH",
  kenya: "KE",
  KENYA: "KE",
  "south africa": "ZA",
  "SOUTH AFRICA": "ZA",

  // Default fallback
  unknown: "NG", // Use Nigeria as fallback instead of XX which doesn't exist
  UNKNOWN: "NG",
};

const countryCoordinates: Record<string, { lat: number; lng: number }> = {
  NIGERIA: { lat: 9.082, lng: 8.6753 },
  "UNITED KINGDOM": { lat: 55.3781, lng: -3.436 },
  "UNITED ARAB EMIRATES": { lat: 23.4241, lng: 53.8478 },
  USA: { lat: 38.9637, lng: -95.7129 },
  CANADA: { lat: 56.1304, lng: -106.3468 },
  BRAZIL: { lat: -14.235, lng: -51.9253 },
  RUSSIA: { lat: 61.524, lng: 105.3188 },
  CHINA: { lat: 35.8617, lng: 104.1954 },
  AUSTRALIA: { lat: -25.2744, lng: 133.7751 },
  UNKNOWN: { lat: 0, lng: 0 },
};

function getCoords(countryName: string | null): { lat: number; lng: number } {
  if (!countryName) return countryCoordinates["NIGERIA"]; // Default to Nigeria if no country name

  const upperCaseCountry = countryName.toUpperCase().trim();

  // Try to get the coordinates from the map
  if (countryCoordinates[upperCaseCountry]) {
    return countryCoordinates[upperCaseCountry];
  }

  // Default to Nigeria
  return countryCoordinates["NIGERIA"];
}

function getCode(countryName: string | null): string {
  if (!countryName) return "NG"; // Default to Nigeria if no country name

  const upperCaseCountry = countryName.toUpperCase().trim();

  // Try to get the country code from the map
  if (countryNameToCode[upperCaseCountry]) {
    return countryNameToCode[upperCaseCountry];
  }

  // Try lowercase version
  if (countryNameToCode[countryName.toLowerCase().trim()]) {
    return countryNameToCode[countryName.toLowerCase().trim()];
  }

  // For unknown countries, create a unique code based on the country name
  // This prevents multiple countries from having the same "NG" code
  if (upperCaseCountry === "UNKNOWN" || upperCaseCountry === "") {
    return "NG"; // Only use NG for truly unknown countries
  }

  // For other countries not in our map, create a unique identifier
  // Take first 2 letters of country name as fallback
  const fallbackCode = upperCaseCountry.replace(/[^A-Z]/g, "").substring(0, 2);
  return fallbackCode.length >= 2 ? fallbackCode : "NG";
}

export async function fetchGlobalDistribution(): Promise<GlobalDistributionApiResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;
  const API_URL = `${API_BASE_URL}/admin/members/all`;

  if (!token) {
    throw new Error("Unauthorized: Missing authentication token.");
  }

  try {
    // Fetch ALL users - Note: This could be inefficient for very large datasets.
    // Consider if the backend can provide an aggregated endpoint directly.
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(
        `API Error (${response.status}) fetching all users: ${errorText}`
      );
      throw new Error(`API Error: ${response.status} ${response.statusText}`);
    }

    const data: UserApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API structure for all users:", data);
      throw new Error("Invalid data format received from API.");
    }

    // --- Aggregation Logic ---
    const usersByCountry: Record<
      string,
      { count: number; countryName: string; countryCode: string }
    > = {};
    data.result.forEach((apiUser: ApiUser) => {
      const countryNameOriginal = (
        apiUser.branch_country ||
        apiUser.country ||
        "Unknown"
      ).trim();
      const countryKey = countryNameOriginal.toUpperCase();

      // Always include the country, even if it's unknown
      const countryCode = getCode(countryNameOriginal);
      if (!usersByCountry[countryKey]) {
        usersByCountry[countryKey] = {
          count: 0,
          countryName: countryNameOriginal || "Nigeria", // Default to Nigeria if no country name
          countryCode: countryCode,
        };
      }
      usersByCountry[countryKey].count += 1;
    });

    const totalUsers = data.result.length;

    // Calculate active users (users with at least one wallet with a non-zero balance)
    let activeUsers = 0;
    data.result.forEach((user: ApiUser) => {
      // Check if user has at least one wallet with a non-zero balance
      const hasActiveWallet =
        user.wallets &&
        user.wallets.some((wallet) => {
          const balance = parseFloat(wallet.balance);
          return !isNaN(balance) && balance > 0;
        });

      if (hasActiveWallet) {
        activeUsers++;
      }
    });

    // Calculate active user percentage
    const activeUserPercentage =
      totalUsers > 0 ? Math.round((activeUsers / totalUsers) * 100) : 0;

    const distributionData: GlobalDistributionDataPoint[] = Object.entries(
      usersByCountry
    ).map(([countryKey, { count, countryName, countryCode }]) => {
      const coords = getCoords(countryKey);
      // Include all countries, even if coordinates are unknown
      const percentage =
        totalUsers > 0
          ? parseFloat(((count / totalUsers) * 100).toFixed(1))
          : 0;
      return {
        id: countryCode,
        country: countryName || "Nigeria", // Default to Nigeria if no country name
        users: count,
        percentage: percentage,
        lat: coords.lat,
        lng: coords.lng,
      };
    });

    distributionData.sort((a, b) => b.users - a.users);

    const apiResponse: GlobalDistributionApiResponse = {
      message: "success",
      totalUsers: totalUsers,
      activeUserPercentage: activeUserPercentage,
      data: distributionData,
    };

    return apiResponse;
  } catch (error) {
    console.error("SERVER: Error in fetchGlobalDistribution:", error);
    throw error;
  }
}
