// lib/fetchBranches.ts
"use server";

import { cookies } from "next/headers";
import type {
  Branch,
  BranchesApiResponse,
  NewBranchPayload,
  AddBranchApiResponse,
  UpdateBranchPayload,
  UpdateBranchApiResponse,
} from "@/types/branch";

const BASE_URL = process.env.BASE_URL;

export async function fetchBranches(): Promise<{ branches: Branch[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_URL = `${BASE_URL}/admin/branches/all`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(
        `API Error fetching branches (${response.status}):`,
        errorData
      );
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Failed to fetch branches."
        }`
      );
    }

    const data: BranchesApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API response structure for branches:", data);
      throw new Error("Invalid data format received for branches.");
    }

    return { branches: data.result };
  } catch (error) {
    console.error("Error in fetchBranches:", error);
    throw new Error("Could not fetch branches. Please try again later.");
  }
}

// --- Update Branch ---
export async function updateBranch(
  id: number,
  payload: UpdateBranchPayload
): Promise<UpdateBranchApiResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_URL = `${BASE_URL}/admin/branches/${id}/update`;

  try {
    const response = await fetch(API_URL, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
    });

    const data: UpdateBranchApiResponse = await response.json();

    if (!response.ok) {
      console.error(
        `API Error updating branch ${id} (${response.status}):`,
        data || "No response body"
      );
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          data?.message || "Failed to update branch."
        }`
      );
    }

    if (data.message !== "success" || data.result !== "success") {
      console.warn(
        "Branch updated, but response structure might be unexpected."
      );
    }

    return data;
  } catch (error) {
    console.error(`Error in updateBranch for ID ${id}:`, error);
    if (error instanceof Error && error.message.startsWith("API Error:")) {
      throw error;
    }
    throw new Error("Could not update branch. Please try again later.");
  }
}

export async function addBranch(
  payload: NewBranchPayload
): Promise<AddBranchApiResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_URL = `${BASE_URL}/admin/branches/new`;

  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
    });

    const data: AddBranchApiResponse = await response.json();

    if (!response.ok) {
      console.error(
        `API Error adding branch (${response.status}):`,
        data || "No response body"
      );
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          data?.message || "Failed to add branch."
        }`
      );
    }

    if (data.message !== "success" || data.result !== "success") {
      console.error("Unexpected success response structure:", data);
      console.warn("Branch added, but response structure might be unexpected.");
    }

    return data;
  } catch (error) {
    console.error("Error in addBranch:", error);
    if (error instanceof Error && error.message.startsWith("API Error:")) {
      throw error;
    }
    throw new Error("Could not add branch. Please try again later.");
  }
}
