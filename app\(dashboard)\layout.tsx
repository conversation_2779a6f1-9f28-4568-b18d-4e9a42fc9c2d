import type React from "react";
import Header from "@/components/header";
import Navigation from "@/components/navigation";
import { prefetchBranches } from "@/lib/prefetchBranches";
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { getQueryClient } from "@/store/get-query-client";

export default async function DashboardLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const queryClient = getQueryClient();
  await prefetchBranches();

  return (
    <div className="min-h-screen bg-[#F1F1F1]">
      <HydrationBoundary state={dehydrate(queryClient)}>
        <Header />
        <Navigation />
        <div className="flex flex-col flex-1">{children}</div>
      </HydrationBoundary>
    </div>
  );
}
