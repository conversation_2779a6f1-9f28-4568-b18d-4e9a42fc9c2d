// components/transactions/TransactionTableEmpty.tsx
"use client";

import type React from "react";
import { Search } from "lucide-react";
import { Button } from "@/components/ui/button";
import { TableRow, TableCell } from "@/components/ui/table";

interface TransactionTableEmptyProps {
  activeFilterCount: number;
  clearAllFilters: () => void;
}

export default function TransactionTableEmpty({
  activeFilterCount,
  clearAllFilters,
}: TransactionTableEmptyProps) {
  return (
    <TableRow>
      <TableCell colSpan={8} className="text-center py-8">
        <div className="h-[300px] flex flex-col items-center justify-center">
          <div className="bg-gray-100 rounded-full p-3 mb-3">
            <Search className="h-6 w-6 text-gray-400" />
          </div>
          <h3 className="text-lg font-medium mb-1">No transactions found</h3>
          <p className="text-gray-500 mb-4">
            Try adjusting your filters or search criteria.
          </p>
          {/* Only show clear filters if filters are active */}
          {activeFilterCount > 0 && (
            <Button
              variant="outline"
              onClick={clearAllFilters}
              className="border-[#866cef] text-[#866cef]"
            >
              Clear all filters
            </Button>
          )}
        </div>
      </TableCell>
    </TableRow>
  );
}
