"use server";

import { cookies } from "next/headers";

export interface UserTransaction {
  id: string;
  member_id: string;
  transaction_reference: string | null;
  transaction_id: string;
  transaction_type: number;
  status: number;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  transaction_amount: string;
  transaction_date: string;
  response_code: string;
  provider: string;
  payment_mode: string;
  amount_paid: string;
  currency: string;
  branch_id: number;
  wallet_id: string | null;
  transaction_type_string: string;
  transaction_status_string: string;
}

interface UserTransactionsApiResponse {
  message: string;
  result: UserTransaction[];
  status: number;
}

export async function fetchUserTransactions(
  memberId: string
): Promise<{ transactions: UserTransaction[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  const API_BASE_URL = process.env.BASE_URL;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  if (!memberId) {
    throw new Error("Member ID is required to fetch transactions.");
  }

  const API_URL = `${API_BASE_URL}/admin/transactions/all?member_id=${memberId}`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }

      console.error(`API Error Response (${response.status}):`, errorData);

      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to fetch user transactions.";

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: UserTransactionsApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API response structure:", data);
      throw new Error(
        "Failed to fetch user transactions or invalid data format."
      );
    }

    return { transactions: data.result };
  } catch (error) {
    console.error("Error fetching or processing user transactions:", error);
    throw error;
  }
}
