// app/transactions/page.tsx (Server Component)
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import Transactions from "@/components/transactions/transactions";
import { getQueryClient } from "@/store/get-query-client";
import { FetchTransactionsParams } from "@/types/transaction";
import { fetchTransactions } from "@/lib/fetchTransactions";

type SearchParams = {
  searchParams: Promise<{
    [key: string]: string | string[] | undefined;
  }>;
};

export default async function TransactionsPage({ searchParams }: SearchParams) {
  const queryClient = getQueryClient();

  const resolvedParams = await searchParams;

  const initialQuery = resolvedParams.query as string | undefined;
  const initialStartDate = resolvedParams.start as string | undefined;
  const initialEndDate = resolvedParams.end as string | undefined;
  const initialPriceMin = resolvedParams.price_min
    ? parseInt(resolvedParams.price_min as string, 10)
    : undefined;
  const initialPriceMax = resolvedParams.price_max
    ? parseInt(resolvedParams.price_max as string, 10)
    : undefined;

  const initialFetchParams: FetchTransactionsParams = {
    start: initialStartDate,
    end: initialEndDate,
    price_min: initialPriceMin,
    price_max: initialPriceMax,
  };
  const initialQueryKey = ["transactions", initialFetchParams];

  await queryClient.prefetchQuery({
    queryKey: initialQueryKey,
    queryFn: () => fetchTransactions(initialFetchParams),
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Transactions initialSearch={initialQuery} />
    </HydrationBoundary>
  );
}
