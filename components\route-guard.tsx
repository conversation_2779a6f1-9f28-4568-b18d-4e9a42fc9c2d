"use client";

import { useAuth } from "@/store/auth-provider";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

interface RouteGuardProps {
  children: React.ReactNode;
}

export default function RouteGuard({ children }: RouteGuardProps) {
  const { user } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  const restrictedPaths = ["/overview", "/transactions", "/users"];

  useEffect(() => {
    // If user is Support and trying to access a restricted path, redirect to declaration
    if (user && user.role === "Support" && restrictedPaths.includes(pathname)) {
      router.replace("/declaration");
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, pathname, router]);

  return <>{children}</>;
}
