"use client";

import type React from "react";
import { createContext, useContext, useState, useEffect } from "react";
import { usePathname, useRouter } from "next/navigation";
import { setCookie, deleteCookie, getCookie } from "cookies-next";
import { toast } from "sonner";

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
}

const ROLE = {
  ADMIN: "Admin",
  MANAGER: "Manager",
  SUPPORT: "Support",
};

interface AuthContextType {
  user: User | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const pathName = usePathname();

  useEffect(() => {
    const token = getCookie("token");
    if (token) {
      const fetchAdminInfo = async (token: string) => {
        setIsLoading(true);
        try {
          const response = await fetch(
            `${process.env.NEXT_PUBLIC_BASE_URL}/admin/manager/info`,
            {
              method: "GET",
              headers: {
                accept: "application/json",
                Authorization: `Bearer ${token}`,
              },
            }
          );

          if (response.status === 401 || response.status === 403) {
            deleteCookie("token");
            setUser(null);
            if (pathName !== "/login") {
              router.push("/login");
            }
            return;
          }

          if (!response.ok) {
            console.error("Failed to fetch admin info:", response);

            return;
          }

          const data = await response.json();
          setUser({
            id: data.result.id,
            firstName: data.result.first_name,
            lastName: data.result.last_name,
            email: data.result.email,
            role: data.result.role,
          });
        } catch (error) {
          console.error("Error fetching admin info:", error);
          deleteCookie("token");
          setUser(null);
          if (pathName !== "/login") {
            router.push("/login");
          }
        } finally {
          setIsLoading(false);
        }
      };

      fetchAdminInfo(token as string);
    } else {
      setIsLoading(false);
    }
  }, [pathName, router]);

  const login = async (email: string, password: string) => {
    setIsLoading(true);

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_BASE_URL}/admin/auth/login`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ email, password }),
        }
      );

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.message || "Login failed. Please check your credentials."
        );
      }

      if (
        !data.result.role ||
        ![ROLE.ADMIN, ROLE.MANAGER, ROLE.SUPPORT].includes(data.result.role)
      ) {
        throw new Error(
          "Access denied. Only Admin, Manager, and Support users can login."
        );
      }

      const userData: User = {
        id: data.result.id,
        firstName: data.result.first_name,
        lastName: data.result.last_name,
        email: data.result.email,
        role: data.result.role,
      };

      // Set user data and token
      setUser(userData);
      setCookie("token", data.result.token, { maxAge: 60 * 60 * 24 * 7 });
      toast("Login successful!");

      // Small delay to ensure token is properly set before redirecting
      setTimeout(() => {
        // Redirect Support users to Declaration page, others to Overview
        if (userData.role === ROLE.SUPPORT) {
          router.push("/declaration");
        } else {
          router.push("/overview");
        }
      }, 100);
    } catch (error) {
      console.error("Login failed:", error);
      toast(
        error instanceof Error
          ? error.message
          : "An error occurred during login."
      );
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const logout = () => {
    setUser(null);
    deleteCookie("token");
    toast("Logged out successfully!");
    router.push("/login");
  };

  return (
    <AuthContext.Provider value={{ user, login, logout, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
