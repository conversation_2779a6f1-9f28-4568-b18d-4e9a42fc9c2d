"use server";

import type {
  TotalGivingApiResponse,
  TotalGivingAnalyticsItem,
} from "@/types/giving";
import { cookies } from "next/headers";

interface FetchTotalGivingParams {
  userId: string;
  month: string;
  year: string;
  currency: string;
}

export const fetchTotalGivingAnalytics = async ({
  userId,
  month,
  year,
  currency,
}: FetchTotalGivingParams): Promise<TotalGivingAnalyticsItem[]> => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!userId || !month || !year || !currency) {
    throw new Error(
      "Missing required parameters for fetching total giving analytics."
    );
  }
  const API_URL = `${process.env.BASE_URL}/admin/members/${userId}/total-giving-analytics?month=${month}&currency=${currency}&year=${year}`;
  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorBody = "Unknown error";
      try {
        const errorData = await response.json();
        errorBody = errorData.message || JSON.stringify(errorData);
      } catch {
        // Ignore if error body isn't valid JSON
      }
      throw new Error(
        `API request failed with status ${response.status}: ${errorBody}`
      );
    }

    const data: TotalGivingApiResponse = await response.json();

    if (data.status !== 200) {
      throw new Error(
        `API returned an error status ${data.status}: ${data.message}`
      );
    }
    return data.result || [];
  } catch (error) {
    console.error("Error fetching total giving analytics:", error);
    throw error;
  }
};
