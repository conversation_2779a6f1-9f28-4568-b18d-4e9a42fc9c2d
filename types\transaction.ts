// Define the structure of a transaction coming from the API
export interface ApiTransaction {
  id: string;
  member_id: string;
  transaction_reference: string | null;
  transaction_id: string;
  transaction_type: number;
  transaction_status: number;
  email: string;
  first_name: string;
  last_name: string;
  phone: string;
  transaction_amount: string;
  transaction_date: string;
  response_code: string | null;
  provider: string;
  payment_mode: string;
  amount_paid: string;
  currency: string;
  branch_id: number;
  wallet_id: string | null;
  transaction_type_string: string;
  transaction_status_string: string;
}

// Define the structure expected by the UI component
export interface Transaction {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: string;
  category: string;
  amount: string;
  amountValue: number;
  currency: string; // Added currency field
  date: string;
  status: string;
}

// Define the structure of the API response (remains the same)
export interface ApiResponse {
  message: string;
  result: ApiTransaction[];
}

// Define the parameters for our fetching function
export interface FetchTransactionsParams {
  start?: string;
  end?: string;
  price_min?: number;
  price_max?: number;
  export?: boolean;
  member_id?: number;
  period?: "day" | "week" | "month" | "year";
  location_id?: number;
  currency?: string; // Added currency filter
}
