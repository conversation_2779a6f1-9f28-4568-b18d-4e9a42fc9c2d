"use server";

import { cookies } from "next/headers";

export interface VerifyPaymentResult {
  transaction_reference: string;
  status: string;
  IsCreditToWallet: boolean;
  IsCompleted: boolean;
}

export interface VerifyPaymentResponse {
  message: string;
  result: VerifyPaymentResult;
  status: number;
}

// Interface for the component to use (normalized)
export interface VerifyPaymentData {
  isCompleted: boolean;
  isCreditToWallet: boolean;
  status: string;
  transaction_reference: string;
}

const API_BASE_URL = process.env.BASE_URL;

export async function verifyPayment(
  transactionId: string,
  providerId: string
): Promise<VerifyPaymentData> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/payments/verify?reference=${encodeURIComponent(
    transactionId
  )}&providerId=${encodeURIComponent(providerId)}`;

  try {
    const response = await fetch(API_URL, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);

      // Handle different error response structures
      let errorMessage = "Failed to verify payment.";

      if (typeof errorData === "object" && errorData !== null) {
        // Check for nested message structure
        if ("message" in errorData && typeof errorData.message === "string") {
          errorMessage = errorData.message;
        }
        // Check for error field
        else if ("error" in errorData && typeof errorData.error === "string") {
          errorMessage = errorData.error;
        }
        // Check for detail field
        else if (
          "detail" in errorData &&
          typeof errorData.detail === "string"
        ) {
          errorMessage = errorData.detail;
        }
      }

      throw new Error(
        `Payment verification failed: ${errorMessage} (Status: ${response.status})`
      );
    }

    const data: VerifyPaymentResponse = await response.json();

    // Transform the API response to the normalized format
    return {
      isCompleted: data.result.IsCompleted,
      isCreditToWallet: data.result.IsCreditToWallet,
      status: data.result.status,
      transaction_reference: data.result.transaction_reference,
    };
  } catch (error) {
    console.error("Error verifying payment:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to verify payment. Please try again later.");
  }
}
