// Interface for a single item in the 'result' array for user-specific giving
export interface TotalGivingAnalyticsItem {
  period: string;
  payment_type: string;
  total_amount: string;
  total_count: number;
}

// Interface for the overall API response structure for user-specific giving
export interface TotalGivingApiResponse {
  message: string;
  result: TotalGivingAnalyticsItem[];
  status: number;
}

// Parameters for the fetchGivingAnalytics function (dashboard analytics)
export interface GivingAnalyticsParams {
  currency?: string;
  period?: "day" | "week" | "month" | "year";
  location_id?: number;
}

// Structure for a single giving data point (dashboard analytics)
export interface GivingDataPoint {
  period: string;
  payment_type: string;
  total_amount: string;
  total_count: number;
}

// Structure for grouped data by date (dashboard analytics)
export interface GivingGroupedData {
  label: string;
  grouped_data: GivingDataPoint[];
}

// Structure for the API response (dashboard analytics)
export interface GivingAnalyticsApiResponse {
  message: string;
  result: {
    grouped_data: GivingGroupedData[] | null;
  };
  status: number;
}

// Structure for the transformed chart data
export interface ChartDataPoint {
  date: string;
  [key: string]: string | number;
}
