"use client";
import React from "react";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Button } from "@/components/ui/button";
import { Settings, X, RefreshCw } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import Image from "next/image";
import { useState, useEffect, useMemo } from "react";
import { useAuth } from "@/store/auth-provider";
import { useBranch } from "@/store/branch-provider";
import Link from "next/link";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import Logo from "./logo";
import { Skeleton } from "./ui/skeleton";

export default function Header() {
  const { user, logout } = useAuth();
  const {
    selectedBranchId,
    setSelectedBranchId,
    availableBranches,
    isLoading,
    error,
  } = useBranch();
  const [isOpenN, setIsOpenN] = useState(false);
  const [isOpenP, setIsOpenP] = useState(false);

  const [selectedCountry, setSelectedCountry] = useState<string>("");
  const [selectedState, setSelectedState] = useState<string>("");
  const [selectedBranchName, setSelectedBranchName] = useState<string>("");

  // Sample notification data
  const notifications = [
    {
      id: "1",
      type: "request",
      user: {
        name: "Joshua Izunanya",
        avatar: "/placeholder.svg?height=64&width=64",
        initials: "JI",
      },
      action: "sent a request of",
      target: "ST-3453412",
      status: "Pending",
      time: "2 hours ago",
      category: "Support",
    },
    {
      id: "2",
      type: "wallet",
      user: {
        name: "Alex Oyinkasola",
        avatar: "/placeholder.svg?height=64&width=64",
        initials: "AO",
      },
      action: "closed his",
      target: "USD Wallet",
      time: "1 day ago",
      category: "Wallet",
    },
  ];

  const countries = useMemo(() => {
    const uniqueCountries = new Set<string>();
    availableBranches.forEach((branch) => {
      if (branch.country) uniqueCountries.add(branch.country);
    });
    return Array.from(uniqueCountries);
  }, [availableBranches]);

  const states = useMemo(() => {
    if (!selectedCountry) return [];

    const uniqueStates = new Set<string>();
    availableBranches
      .filter((branch) => branch.country === selectedCountry)
      .forEach((branch) => {
        if (branch.state) uniqueStates.add(branch.state);
      });
    return Array.from(uniqueStates);
  }, [availableBranches, selectedCountry]);

  const branches = useMemo(() => {
    if (!selectedCountry || !selectedState) return [];

    return availableBranches.filter(
      (branch) =>
        branch.country === selectedCountry && branch.state === selectedState
    );
  }, [availableBranches, selectedCountry, selectedState]);

  const handleCountryChange = (value: string) => {
    // Only allow admin users to change country
    if (user?.role === "Admin") {
      setSelectedCountry(value);
      setSelectedState("");
      setSelectedBranchName("");
      setSelectedBranchId(null);
    }
  };

  const handleStateChange = (value: string) => {
    // Only allow admin users to change state
    if (user?.role === "Admin") {
      setSelectedState(value);
      setSelectedBranchName("");
      setSelectedBranchId(null);
    }
  };



  const handleBranchChange = (value: string) => {
    // Only allow admin users to change branchs
    if (user?.role === "Admin") {
      setSelectedBranchName(value);

      const branch = branches.find((b) => b.name === value);
      if (branch) {
        if (!selectedCountry || !selectedState) {
          setSelectedCountry(branch.country || "");
          setSelectedState(branch.state || "");
        }
        setSelectedBranchId(branch.id);
      } else {
        setSelectedBranchId(null);
      }
    }
  };

  useEffect(() => {
    if (availableBranches.length > 0 && selectedBranchId) {
      const savedBranch = availableBranches.find(
        (b) => b.id === selectedBranchId
      );

      if (savedBranch) {
        setSelectedCountry(savedBranch.country || "");
        setSelectedState(savedBranch.state || "");
        setSelectedBranchName(savedBranch.name);
      }
    }
  }, [availableBranches, selectedBranchId]);

  useEffect(() => {
    // Only auto-select for admin users
    if (user?.role === "Admin") {
      if (countries.length > 0 && !selectedCountry && !selectedBranchId) {
        setSelectedCountry(countries[0]);
      }
    }
  }, [countries, selectedCountry, selectedBranchId, user?.role]);

  useEffect(() => {
    if (user?.role === "Admin") {
      // Auto-select first state if none selected
      if (states.length > 0 && !selectedState && !selectedBranchId) {
        setSelectedState(states[0]);
      }
    }
  }, [states, selectedState, selectedBranchId, user?.role]);

  useEffect(() => {
    if (user?.role === "Admin") {
      if (branches.length > 0 && !selectedBranchName && !selectedBranchId) {
        setSelectedBranchName(branches[0].name);
        setSelectedBranchId(branches[0].id);
      }
    } else if (availableBranches.length > 0 && !selectedBranchId) {
      setSelectedBranchName(availableBranches[0].name);
      setSelectedCountry(availableBranches[0].country || "");
      setSelectedState(availableBranches[0].state || "");
      setSelectedBranchId(availableBranches[0].id);
    }
  }, [
    branches,
    selectedBranchName,
    setSelectedBranchId,
    selectedBranchId,
    user?.role,
    availableBranches,
    setSelectedCountry,
    setSelectedState,
  ]);

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="container mx-auto md:px-6 md:py-6 px-4 py-4 flex justify-between items-center">
      <Logo />
      <div className="flex items-center space-x-4">
        <div className="bg-[#F8F8F8] rounded-full px-4 py-1 flex items-center space-x-7 shadow-sm">
          {isLoading ? (
            <Skeleton className="h-6 w-32 rounded" />
          ) : error && !availableBranches.length ? (
            <div
              className="text-sm text-red-500 cursor-pointer flex items-center"
              onClick={() => window.location.reload()}
            >
              Error loading branches
              <RefreshCw className="ml-2 h-3 w-3" />
            </div>
          ) : user?.role !== "Admin" ? (
            <div className="flex items-center space-x-7">
              <div className="text-sm py-2 px-1 min-w-[100px] flex items-center">
                <span>{selectedCountry || "Country"}</span>
              </div>
              <div className="text-sm py-2 px-1 min-w-[100px] flex items-center">
                <span>{selectedState || "State"}</span>
              </div>
              <div className="text-sm py-2 px-1 min-w-[100px] flex items-center">
                <span>{selectedBranchName || "No branch assigned"}</span>
              </div>
            </div>
          ) : (
            <>
              <Select
                value={selectedCountry}
                onValueChange={handleCountryChange}
              >
                <SelectTrigger className="bg-transparent shadow-none border-none text-sm focus:outline-none">
                  <SelectValue placeholder="Select Country" />
                </SelectTrigger>
                <SelectContent>
                  {countries.map((country) => (
                    <SelectItem key={country} value={country}>
                      {country}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedState}
                onValueChange={handleStateChange}
                disabled={!selectedCountry || states.length === 0}
              >
                <SelectTrigger className="bg-transparent shadow-none border-none text-sm focus:outline-none">
                  <SelectValue placeholder="Select State" />
                </SelectTrigger>
                <SelectContent>
                  {states.map((state) => (
                    <SelectItem key={state} value={state}>
                      {state}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedBranchName}
                onValueChange={handleBranchChange}
                disabled={!selectedState || branches.length === 0}
              >
                <SelectTrigger className="bg-transparent shadow-none border-none text-sm focus:outline-none">
                  <SelectValue placeholder="Select Branch" />
                </SelectTrigger>
                <SelectContent>
                  {branches.map((branch) => (
                    <SelectItem key={branch.id} value={branch.name}>
                      {branch.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </>
          )}
        </div>

        {/* Notification Dropdown */}
        <DropdownMenu open={isOpenN} onOpenChange={setIsOpenN}>
          <DropdownMenuTrigger asChild>
            <div className="relative bg-[#F8F8F8] rounded-full px-[1px] py-[1px] flex items-center shadow-sm cursor-pointer">
              <div className="w-5 h-5 bg-red-500 rounded-full flex items-center justify-center absolute -top-1 -right-1 text-white text-xs">
                2
              </div>
              <Button variant="ghost" size="icon" className="rounded-full">
                <svg
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M18 8A6 6 0 0 0 6 8c0 7-3 9-3 9h18s-3-2-3-9"
                    stroke="#000"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M13.73 21a2 2 0 0 1-3.46 0"
                    stroke="#000"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Button>
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[400px] p-0 rounded-2xl"
            align="end"
          >
            <div className="flex items-center justify-between p-4">
              <h2 className="text-sm">NOTIFICATIONS (2)</h2>
              <button onClick={() => setIsOpenN(false)} className="">
                <X className="h-5 w-5" />
              </button>
            </div>

            <Tabs defaultValue="unread" className="w-full">
              <TabsList className="border-b border-[#DDDDDD] w-full flex justify-start shadow-none bg-transparent rounded-none h-auto">
                <TabsTrigger
                  value="unread"
                  className="text-sm font-medium data-[state=active]:shadow-none rounded-none py-2 data-[state=active]:border-none data-[state=active]:border-transparent"
                >
                  UNREAD
                </TabsTrigger>
                <TabsTrigger
                  value="read"
                  className="text-sm font-medium py-2 rounded-none data-[state=active]:shadow-none data-[state=active]:border-none data-[state=active]:border-transparent"
                >
                  READ
                </TabsTrigger>
              </TabsList>

              <TabsContent value="unread" className="mt-0">
                <div className="max-h-[400px] overflow-y-auto">
                  {notifications.map((notification) => (
                    <div key={notification.id} className="p-4">
                      <div className="flex">
                        <Avatar className="h-10 w-10 mr-4">
                          <AvatarImage
                            src={notification.user.avatar}
                            alt={notification.user.name}
                          />
                          <AvatarFallback>
                            {notification.user.initials}
                          </AvatarFallback>
                        </Avatar>
                        <div className="flex-1">
                          <div className="mb-2">
                            <span className=" text-sm">
                              {notification.user.name}
                            </span>{" "}
                            <span className="text-gray-500">
                              {notification.action}
                            </span>{" "}
                            <span className="">{notification.target}</span>
                          </div>

                          {notification.status && (
                            <div className="mb-3">
                              <span className="px-3 py-1 bg-[#ffe580] text-black rounded-md font-medium">
                                {notification.status}
                              </span>
                            </div>
                          )}

                          <div className="text-gray-500 text-xs">
                            <span>{notification.time}</span>
                            <span className="mx-2">•</span>
                            <span>{notification.category}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </TabsContent>

              <TabsContent value="read" className="mt-0">
                <div className="p-6 text-center text-gray-500">
                  No read notifications
                </div>
              </TabsContent>
            </Tabs>

            <div className="p-4 flex justify-end border-t">
              <button className="text-[#866cef] text-sm font-medium hover:text-[#7057d3]">
                Mark All As Read
              </button>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Settings Dialog */}
        <Link
          href="/settings"
          className="relative bg-[#F8F8F8] rounded-full p-2 flex items-center shadow-sm cursor-pointer"
          prefetch
        >
          <Settings size={20} />
        </Link>

        {/* Profile Dropdown */}
        <DropdownMenu open={isOpenP} onOpenChange={setIsOpenP}>
          <DropdownMenuTrigger asChild>
            <Avatar className="cursor-pointer">
              <AvatarImage src={""} alt={user?.firstName} />
              <AvatarFallback>{`${user?.firstName
                ?.charAt(0)
                .toUpperCase()}${user?.lastName
                ?.charAt(0)
                .toUpperCase()}`}</AvatarFallback>
            </Avatar>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-[330px] p-0 rounded-2xl"
            align="end"
          >
            <div className="flex items-center justify-between p-4">
              <h2 className="text-sm">PROFILE</h2>
              <button onClick={() => setIsOpenP(false)} className="">
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="p-4 flex items-center border-b">
              <Avatar className="h-10 w-10 mr-4">
                <AvatarImage src={""} alt={user?.firstName} />
                <AvatarFallback>{`${user?.firstName
                  ?.charAt(0)
                  .toUpperCase()}${user?.lastName
                  ?.charAt(0)
                  .toUpperCase()}`}</AvatarFallback>
              </Avatar>
              <div>
                <h3 className="text-sm">{`
                ${user?.firstName} ${user?.lastName} `}</h3>
                <p className="text-black">{user?.email}</p>
              </div>
            </div>

            <div className="p-4 hover:text-[#866cef]">
              <button
                className="flex items-center text-sm"
                onClick={handleLogout}
              >
                <Image
                  src="/logout.svg"
                  alt="Logout"
                  width={15}
                  height={15}
                  className="mr-3"
                />
                <span>Log Out</span>
              </button>
            </div>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
}
