import React from "react";
import { Search, Download } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface UserHeaderProps {
  userCount: number | string;
  searchQuery: string | null;
  sortBy: string;
  onSearchChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onSortChange: (value: string) => void;
  onExportClick: () => void;
  isExportLoading?: boolean;
}

export function UserHeader({
  userCount,
  searchQuery,
  sortBy,
  onSearchChange,
  onSortChange,
  onExportClick,
  isExportLoading = false,
}: UserHeaderProps) {
  return (
    <div className="flex justify-between items-center mb-6 flex-wrap gap-4">
      <div className="flex items-center space-x-4">
        <h2 className="text-xl font-bold">USERS</h2>
        <span className="text-sm text-gray-500">{userCount}</span>
      </div>
      <div className="flex items-center space-x-4 flex-wrap gap-2">
        <div className="relative flex-grow md:flex-grow-0">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search Users..."
            className="pl-10 w-full md:w-[250px] shadow-none border-none bg-[#F1F1F1] rounded-full"
            value={searchQuery ?? ""}
            onChange={onSearchChange}
          />
        </div>

        <div className="flex items-center bg-[#F1F1F1] rounded-full">
          <Select value={sortBy} onValueChange={onSortChange}>
            <SelectTrigger className="w-auto shadow-none border-none">
              <span className="text-sm mr-1">Sort by:</span>
              <SelectValue placeholder="Date" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Date">Date</SelectItem>
              <SelectItem value="Name">Name</SelectItem>
              {/* Add other sort options if needed */}
            </SelectContent>
          </Select>
        </div>
        <Button
          onClick={onExportClick}
          disabled={isExportLoading}
          className="flex items-center rounded-full"
        >
          <Download className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Export</span>
        </Button>
      </div>
    </div>
  );
}
