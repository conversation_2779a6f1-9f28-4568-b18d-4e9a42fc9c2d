import type { Metada<PERSON> } from "next";
import { Manrope } from "next/font/google";
import "./globals.css";
import "leaflet/dist/leaflet.css";
import { Toaster } from "@/components/ui/sonner";
import { NuqsAdapter } from "nuqs/adapters/next/app";
import { AuthProvider } from "@/store/auth-provider";
import { BranchProvider } from "@/store/branch-provider";
import { Providers } from "@/store/query-providers";
import NextTopLoader from "nextjs-toploader";
import RouteGuard from "@/components/route-guard";

const manrope = Manrope({
  variable: "--font-manrope",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: {
    default: "Storehouse Dashboard",
    template: "%s | Storehouse Dashboard",
  },
  description:
    "Administrative dashboard for Storehouse Giving platform, managing declarations, support, transactions, and users.",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${manrope.variable} antialiased`}>
        <Providers>
          <AuthProvider>
            <BranchProvider>
              <RouteGuard>
                <Toaster />
                <NuqsAdapter>{children}</NuqsAdapter>
              </RouteGuard>
            </BranchProvider>
          </AuthProvider>
        </Providers>
        <NextTopLoader showSpinner={false} color="#ad33e3" />
      </body>
    </html>
  );
}
