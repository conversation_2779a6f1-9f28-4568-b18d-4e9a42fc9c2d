// src/components/dashboard/GlobalDistributionMap.tsx

"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Popup } from "react-leaflet";
import L from "leaflet";
import { Skeleton } from "@/components/ui/skeleton";
import type { GlobalDistributionDataPoint } from "@/types/user";
export interface MapDataPoint {
  id: string;
  lat: number;
  lng: number;
  label: string;
  value: number;
}

// --- Updated Props Interface ---
interface GlobalDistributionMapProps {
  initialData?: GlobalDistributionDataPoint[];
  isLoading: boolean;
  isError: boolean;
  error: Error | null;
  totalUsers?: number;
  activeUserPercentage?: number;
}

// --- Helper: Custom Marker Component ---
const CustomMarker: React.FC<{ point: MapDataPoint }> = ({ point }) => {
  const countryCode = (point.id || "NG").toUpperCase();

  const flagSvgPath = `/flags/3x2/${countryCode}.svg`;

  const markerHtml = `<div class="bg-white shadow rounded px-2 py-1 text-xs font-medium border border-gray-200 flex items-center gap-1.5 whitespace-nowrap">
    <div class="w-4 h-4 rounded flex items-center justify-center bg-gray-50 overflow-hidden">
      <img
        src="${flagSvgPath}"
        alt="${countryCode} flag"
        class="w-full h-full object-cover"
        onerror="this.style.display='none'; this.parentElement.innerHTML='<span>${point.label.charAt(
          0
        )}</span>';"
      />
    </div>
    <span>${point.value.toLocaleString()}</span>
  </div>`;

  const customIcon = L.divIcon({
    html: markerHtml,
    className: "",
    iconSize: undefined,
    iconAnchor: [15, 10],
    popupAnchor: [0, -10],
  });

  return (
    <Marker position={[point.lat, point.lng]} icon={customIcon}>
      <Popup>{point.label}</Popup>
    </Marker>
  );
};

// --- Main Map Component ---
export default function GlobalDistributionMap({
  initialData,
  isLoading,
  isError,
  error,
  totalUsers,
}: GlobalDistributionMapProps) {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);

    try {
      // @ts-expect-error -- Overriding Leaflet's internal method lookup is necessary sometimes
      delete L.Icon.Default.prototype._getIconUrl;

      L.Icon.Default.mergeOptions({
        iconRetinaUrl: "/leaflet/marker-icon-2x.png",
        iconUrl: "/leaflet/marker-icon.png",
        shadowUrl: "/leaflet/marker-shadow.png",
      });
    } catch (_e) {
      console.error("Error configuring Leaflet default icons:", _e);
    }
  }, []);

  const mapData: MapDataPoint[] = React.useMemo(() => {
    if (!isClient) return [];

    if (!initialData || initialData.length === 0) return [];

    // Always use individual country data, filter out countries with 0 users
    return initialData
      .filter((d) => d.users > 0)
      .map(
        (d: GlobalDistributionDataPoint): MapDataPoint => ({
          id: d.id || "NG",
          lat: d.lat,
          lng: d.lng,
          label: `${d.country}: ${d.users.toLocaleString()}`,
          value: d.users,
        })
      );
  }, [isClient, initialData]);

  if (!isClient) {
    return <Skeleton className="h-[350px] w-full rounded-md" />;
  }

  if (isLoading) {
    return <Skeleton className="h-[350px] w-full rounded-md" />;
  }

  if (isError) {
    return (
      <div className="h-[350px] w-full flex items-center justify-center text-center text-destructive bg-destructive/10 rounded-md p-4">
        Error loading map data: <br /> {error?.message || "Unknown error"}
      </div>
    );
  }

  if (mapData.length === 0) {
    return (
      <div className="h-[350px] w-full flex items-center justify-center text-muted-foreground bg-muted/50 rounded-md p-4">
        No map distribution data available.
      </div>
    );
  }

  return (
    <MapContainer
      center={[20, 10]}
      zoom={2}
      scrollWheelZoom={false}
      attributionControl={false}
      style={{
        height: "350px",
        width: "100%",
        borderRadius: "0.375rem",
        backgroundColor: "#f1f5f9",
        zIndex: 0,
      }}
    >
      <TileLayer
        attribution='&copy; <a href="https://carto.com/attributions">CARTO</a>, &copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
        url="https://{s}.basemaps.cartocdn.com/light_nolabels/{z}/{x}/{y}{r}.png"
      />

      {mapData.map((point) =>
        typeof point.lat === "number" &&
        typeof point.lng === "number" &&
        !isNaN(point.lat) &&
        !isNaN(point.lng) ? (
          <CustomMarker key={point.id.toString()} point={point} />
        ) : (
          (console.warn(
            `Skipping marker for ID ${point.id} due to invalid coordinates:`,
            point
          ),
          null)
        )
      )}
    </MapContainer>
  );
}
