"use server";

import { cookies } from "next/headers";

export interface RegisterUserPayload {
  branches: number[];
  email: string;
  first_name: string;
  last_name: string;
  password: string;
  phone: string;
  role_name: string;
}

export interface RegisterUserResponse {
  message: string;
  result: string;
  status: number;
}

export async function registerUser(
  payload: RegisterUserPayload
): Promise<RegisterUserResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  if (!API_BASE_URL) {
    throw new Error("API base URL is undefined. Check environment variables.");
  }

  const API_URL = `${API_BASE_URL}/admin/auth/register`;

  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`API Error Response (${response.status}):`, errorData);
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Check server logs for details."
        }`
      );
    }

    const data: RegisterUserResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error registering user:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to register user. Please try again later.");
  }
}
