"use server";

import { getQueryClient } from "@/store/get-query-client";
import { fetchGivingAnalytics } from "./fetchGivingAnalytics";
import { fetchTransactions } from "./fetchTransactions";
import { fetchDashboardStats } from "./fetchDashboardStats";

export async function prefetchDashboard() {
  const queryClient = getQueryClient();
  await Promise.all([
    // Prefetch giving analytics with default period (week)
    queryClient.prefetchQuery({
      queryKey: ["givingAnalytics", "1W"],
      queryFn: () => fetchGivingAnalytics({ period: "week", currency: "NGN" }),
    }),

    // Prefetch recent transactions
    queryClient.prefetchQuery({
      queryKey: ["transactions", "dashboard"],
      queryFn: () => fetchTransactions({}),
    }),

    // Prefetch dashboard stats
    queryClient.prefetchQuery({
      queryKey: ["dashboardStats"],
      queryFn: () => fetchDashboardStats(),
    }),
  ]);

  return queryClient;
}
