/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import { useState, useMemo } from "react";
import { useBranch } from "@/store/branch-provider";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import { TrendingUp } from "lucide-react";

import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Image from "next/image";
import { fetchTransactions } from "@/lib/fetchTransactions";
import { fetchGivingAnalytics } from "@/lib/fetchGivingAnalytics";
import { fetchDashboardStats } from "@/lib/fetchDashboardStats";
import type { Transaction } from "@/types/transaction";
import type {
  GivingAnalyticsApiResponse,
  ChartDataPoint,
} from "@/types/giving";
import type { DashboardStats } from "@/lib/fetchDashboardStats";
import TransactionTable from "@/components/transactions/TransactionTable";
import { Skeleton } from "@/components/ui/skeleton";
import { format, parseISO } from "date-fns";

const periodMap: Record<string, string> = {
  "24H": "day",
  "1W": "week",
  "1M": "month",
  "1Y": "year",
};

const transformGivingData = (
  data: GivingAnalyticsApiResponse
): ChartDataPoint[] => {
  if (
    !data.result ||
    !data.result.grouped_data ||
    data.result.grouped_data.length === 0
  ) {
    return [];
  }

  return data.result.grouped_data.map((group) => {
    const chartPoint: ChartDataPoint = {
      date: format(parseISO(group.label), "dd MMM"),
    };

    group.grouped_data.forEach((item) => {
      chartPoint[item.payment_type] = parseInt(item.total_amount, 10);
    });

    return chartPoint;
  });
};

const availableCurrencies = ["NGN", "USD", "GBP", "EUR"];

export default function Dashboard() {
  const [activeCategory, setActiveCategory] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState("1W");
  const [selectedCurrency, setSelectedCurrency] = useState("NGN");
  const { selectedBranchId } = useBranch();

  // Fetch giving analytics data
  const {
    data: givingData,
    isLoading: isLoadingGiving,
    error: givingError,
  } = useQuery<GivingAnalyticsApiResponse>({
    queryKey: [
      "givingAnalytics",
      timeRange,
      selectedCurrency,
      selectedBranchId,
    ],
    queryFn: () =>
      fetchGivingAnalytics({
        period: periodMap[timeRange] as "day" | "week" | "month" | "year",
        currency: selectedCurrency,
        location_id: selectedBranchId !== null ? selectedBranchId : undefined,
      }),
    placeholderData: (previousData) => previousData,
  });

  // Transform API data for the chart
  const chartData = useMemo(() => {
    if (!givingData) return [];
    return transformGivingData(givingData);
  }, [givingData]);

  const {
    data: transactionData,
    isLoading: isLoadingTransactions,
    error: transactionsError,
  } = useQuery<{ transactions: Transaction[] }>({
    queryKey: ["transactions", "dashboard", selectedBranchId],
    queryFn: () =>
      fetchTransactions({
        location_id: selectedBranchId !== null ? selectedBranchId : undefined,
      }),
    placeholderData: (previousData) => previousData,
  });

  const recentTransactions = transactionData?.transactions?.slice(0, 10) || [];

  // Fetch dashboard stats
  const {
    data: statsData,
    isLoading: isLoadingStats,
    error: statsError,
  } = useQuery<DashboardStats>({
    queryKey: ["dashboardStats", selectedBranchId],
    queryFn: () =>
      fetchDashboardStats({
        location_id: selectedBranchId !== null ? selectedBranchId : undefined,
      }),
    placeholderData: (previousData) => previousData,
  });

  // Colors for different categories
  const categoryColors = {
    Offering: "#68b29f",
    Tithe: "#866cef",
    Seed: "#ef732f",
    "First-fruit": "#ffcc00",
    Partnership: "#e84c4c",
    "Prophet Offering": "#ad33e3",
  };

  const handleCategoryClick = (category: string) => {
    setActiveCategory(activeCategory === category ? null : category);
  };

  const getCategoryColor = (category: string) => {
    if (activeCategory === null || activeCategory === category) {
      return categoryColors[category as keyof typeof categoryColors];
    }
    return "#e5e4e4";
  };

  const getCategoryOpacity = (category: string) => {
    if (activeCategory === null || activeCategory === category) {
      return 1;
    }
    return 0.3;
  };

  const formatCurrency = (value: number) => {
    const localeMap: Record<string, string> = {
      NGN: "en-NG",
      USD: "en-US",
      GBP: "en-GB",
      EUR: "de-DE",
    };

    const symbolMap: Record<string, string> = {
      NGN: "₦",
      USD: "$",
      GBP: "£",
      EUR: "€",
    };

    return new Intl.NumberFormat(localeMap[selectedCurrency] || "en-US", {
      style: "currency",
      currency: selectedCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    })
      .format(value)
      .replace(
        selectedCurrency,
        symbolMap[selectedCurrency] || selectedCurrency
      );
  };

  const getStatusColor = (status: string) => {
    const upperStatus = status?.toUpperCase() || "UNKNOWN";
    switch (upperStatus) {
      case "COMPLETED":
      case "SUCCESS":
        return "bg-[#c5ead1] text-[#51ac32]";
      case "PENDING":
        return "bg-[#ffe580] text-[#dca821]";
      case "REFUNDED":
        return "bg-[#e5e4e4] text-[#707070]";
      case "CANCELLED":
      case "FAILED":
        return "bg-[#f7b997] text-[#ef732f]";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const totalAmount = useMemo(() => {
    return chartData.reduce((sum, day) => {
      return (
        sum +
        Object.entries(day)
          .filter(([key]) => key !== "date")
          .reduce((daySum, [_, value]) => daySum + (Number(value) || 0), 0)
      );
    }, 0);
  }, [chartData]);

  const paymentTypes = useMemo(() => {
    const types = new Set<string>();
    chartData.forEach((day) => {
      Object.keys(day).forEach((key) => {
        if (key !== "date") types.add(key);
      });
    });
    return Array.from(types);
  }, [chartData]);

  return (
    <main className="container mx-auto px-6 grid grid-cols-1 lg:grid-cols-4 gap-3">
      {/* Givings Section - Takes 3/4 of the width on large screens */}
      <div className="lg:col-span-3">
        <Card className="overflow-hidden">
          <div className="p-6">
            <div className="flex justify-between items-center mb-2">
              <h2 className="text-xl font-bold">GIVINGS</h2>
              <div className="flex items-center space-x-4">
                <Tabs value={timeRange} onValueChange={setTimeRange}>
                  <TabsList>
                    <TabsTrigger value="24H" className="text-xs">
                      24H
                    </TabsTrigger>
                    <TabsTrigger value="1W" className="text-xs">
                      1W
                    </TabsTrigger>
                    <TabsTrigger value="1M" className="text-xs">
                      1M
                    </TabsTrigger>
                    <TabsTrigger value="1Y" className="text-xs">
                      1Y
                    </TabsTrigger>
                  </TabsList>
                </Tabs>

                <div className="flex items-center">
                  <span className="text-xs text-gray-500 mr-2">Currency:</span>
                  <Select
                    value={selectedCurrency}
                    onValueChange={setSelectedCurrency}
                  >
                    <SelectTrigger className="w-[80px] h-8 text-xs">
                      <SelectValue placeholder="Currency">
                        {selectedCurrency}
                      </SelectValue>
                    </SelectTrigger>
                    <SelectContent>
                      {availableCurrencies.map((currency) => (
                        <SelectItem
                          key={currency}
                          value={currency}
                          className="text-xs"
                        >
                          <div className="flex items-center">
                            <span className="mr-2">
                              {currency === "NGN"
                                ? "₦"
                                : currency === "USD"
                                ? "$"
                                : currency === "GBP"
                                ? "£"
                                : currency === "EUR"
                                ? "€"
                                : ""}
                            </span>
                            {currency}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Category Legend */}
            <div className="flex flex-wrap gap-4 mb-2">
              {isLoadingGiving ? (
                <div className="flex gap-2">
                  <Skeleton className="w-20 h-6" />
                  <Skeleton className="w-20 h-6" />
                  <Skeleton className="w-20 h-6" />
                </div>
              ) : givingError ? (
                <div className="text-red-500">Error loading categories</div>
              ) : paymentTypes.length === 0 ? (
                <div className="text-gray-500">No payment types available</div>
              ) : (
                paymentTypes.map((category) => {
                  const color =
                    categoryColors[category as keyof typeof categoryColors] ||
                    "#999999";
                  return (
                    <button
                      key={`category-${category}`}
                      onClick={() => handleCategoryClick(category)}
                      className={`flex items-center space-x-2 cursor-pointer transition-all duration-200 px-2 py-1 rounded-md ${
                        activeCategory === category ? "bg-gray-100" : ""
                      }`}
                      style={{ opacity: getCategoryOpacity(category) }}
                    >
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: color }}
                      />
                      <span className="text-sm font-medium">{category}</span>
                    </button>
                  );
                })
              )}
            </div>

            {/* Total Amount */}
            <div className="flex items-center space-x-4 mb-4">
              {isLoadingGiving ? (
                <>
                  <Skeleton className="w-32 h-8" />
                  <span className="mx-2 text-[#9C9A9A]">|</span>
                  <Skeleton className="w-48 h-5" />
                </>
              ) : givingError ? (
                <div className="text-red-500">Error loading data</div>
              ) : (
                <>
                  <h3 className="text-3xl">{formatCurrency(totalAmount)}</h3>
                  {chartData.length > 0 && (
                    <>
                      <span className="mx-2 text-[#9C9A9A]">|</span>
                      <div className="text-sm text-[#9C9A9A]">
                        {`${chartData[0].date} - ${
                          chartData[chartData.length - 1].date
                        }, ${new Date().getFullYear()}`}
                      </div>
                    </>
                  )}
                </>
              )}
            </div>

            {/* Chart */}
            <div className="h-[350px]">
              {isLoadingGiving ? (
                <div className="h-full w-full flex items-center justify-center">
                  <Skeleton className="h-[300px] w-full" />
                </div>
              ) : givingError ? (
                <div className="h-full w-full flex items-center justify-center text-red-500">
                  Error loading chart data. Please try again later.
                </div>
              ) : chartData.length === 0 ? (
                <div className="h-full w-full flex flex-col items-center justify-center text-gray-500">
                  <p className="mb-2">
                    No data available for {selectedCurrency} in the selected
                    time period.
                  </p>
                  <p className="text-sm">
                    Try selecting a different currency or time period.
                  </p>
                </div>
              ) : (
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                    barGap={2}
                    barSize={activeCategory === null ? 25 : 30}
                  >
                    <CartesianGrid strokeDasharray="3 3" vertical={false} />
                    <XAxis
                      dataKey="date"
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12 }}
                    />
                    <YAxis
                      axisLine={false}
                      tickLine={false}
                      tick={{ fontSize: 12 }}
                      tickFormatter={(value) => {
                        const symbolMap: Record<string, string> = {
                          NGN: "₦",
                          USD: "$",
                          GBP: "£",
                          EUR: "€",
                        };
                        return `${
                          symbolMap[selectedCurrency] || selectedCurrency
                        }${value / 1000}k`;
                      }}
                    />
                    <Tooltip
                      formatter={(value, name) => [
                        `${formatCurrency(value as number)}`,
                        name,
                      ]}
                      labelFormatter={(label) => `Date: ${label}`}
                      isAnimationActive={true}
                      animationDuration={300}
                      contentStyle={{
                        backgroundColor: "rgba(255, 255, 255, 0.95)",
                        border: "1px solid #f0f0f0",
                        borderRadius: "4px",
                        padding: "8px 12px",
                        boxShadow: "0 2px 8px rgba(0, 0, 0, 0.1)",
                      }}
                      itemStyle={{ padding: "2px 0" }}
                    />
                    {/* Dynamic Bar components based on available payment types */}
                    {paymentTypes.map((type) => (
                      <Bar
                        key={`bar-${type}`}
                        name={type}
                        dataKey={type}
                        fill={getCategoryColor(type)}
                        opacity={getCategoryOpacity(type)}
                        radius={[2, 2, 0, 0]}
                      />
                    ))}
                  </BarChart>
                </ResponsiveContainer>
              )}
            </div>
          </div>
        </Card>
      </div>

      {/* Stats Cards - Takes 1/4 of the width on large screens */}
      <div className="lg:col-span-1 flex flex-col h-[527px] gap-3">
        {/* Match chart height */}
        <Card>
          <div className="p-6 h-fit flex flex-col justify-between">
            <div>
              <div className="flex items-center mb-6">
                <Image
                  src="/users-icon.svg"
                  alt="User Icon"
                  width={24}
                  height={24}
                  className="rounded-sm mr-3"
                />
                <h3 className="text-sm font-medium">TOTAL USERS</h3>
              </div>
              {isLoadingStats ? (
                <Skeleton className="h-10 w-32 mb-3" />
              ) : statsError ? (
                <div className="text-red-500 text-3xl font-bold mb-3">
                  Error
                </div>
              ) : (
                <div className="text-3xl font-bold mb-3">
                  {statsData?.total_users?.toLocaleString() || "0"}
                </div>
              )}
            </div>
            <div className="flex items-center text-[#51ac32]">
              <TrendingUp className="h-4 w-4" />
              <span className="text-sm ml-1">Updated in real-time</span>
            </div>
          </div>
        </Card>
        <Card className="flex-1">
          <div className="h-full flex flex-col gap-y-24 p-6">
            <div className="space-y-5">
              <div className="flex items-center">
                <Image
                  src="/t-country.svg"
                  alt="Country Icon"
                  width={24}
                  height={24}
                  className="rounded-sm mr-3"
                />
                <h3 className="text-sm font-medium">TOTAL COUNTRIES</h3>
              </div>
              {isLoadingStats ? (
                <Skeleton className="h-10 w-20" />
              ) : statsError ? (
                <div className="text-red-500 text-3xl font-bold">Error</div>
              ) : (
                <div className="text-3xl font-bold">
                  {statsData?.total_count?.toLocaleString() || "0"}
                </div>
              )}
            </div>

            <div className="space-y-5">
              <div className="flex items-center">
                <Image
                  src="/t-request.svg"
                  alt="Request Icon"
                  width={24}
                  height={24}
                  className="rounded-sm mr-3"
                />
                <h3 className="text-sm font-medium">TOTAL REQUESTS</h3>
              </div>
              {isLoadingStats ? (
                <Skeleton className="h-10 w-20" />
              ) : statsError ? (
                <div className="text-red-500 text-3xl font-bold">Error</div>
              ) : (
                <div className="text-3xl font-bold">
                  {statsData?.total_requests?.toLocaleString() || "0"}
                </div>
              )}
              <div className="text-sm text-[#51ac32]">Updated in real-time</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Transactions Section - Full width on all screens */}
      <div className="lg:col-span-4">
        <Card>
          <div className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-medium">RECENT TRANSACTIONS</h2>
              <Link href="/transactions" passHref>
                <Button variant="ghost" size="sm" className="text-[#866cef]">
                  View all
                </Button>
              </Link>
            </div>

            {/* Conditional Rendering based on fetch state */}
            {isLoadingTransactions ? (
              <div className="space-y-2">
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
                <Skeleton className="h-10 w-full" />
              </div>
            ) : transactionsError ? (
              <div className="text-center text-red-500 py-4">
                Failed to load transactions. Please try again later.
              </div>
            ) : recentTransactions.length === 0 ? (
              // Empty State
              <div className="text-center text-gray-500 py-4">
                No recent transactions found.
              </div>
            ) : (
              <TransactionTable
                currentItems={recentTransactions}
                getStatusColor={getStatusColor}
              />
            )}
          </div>
        </Card>
      </div>
    </main>
  );
}
