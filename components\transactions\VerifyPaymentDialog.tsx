"use client";

import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { toast } from "sonner";
import { verifyPayment, type VerifyPaymentResponse } from "@/lib/verifyPayment";
import { CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";

interface VerifyPaymentDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export default function VerifyPaymentDialog({
  isOpen,
  onClose,
}: VerifyPaymentDialogProps) {
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    transactionId: "",
    providerId: "",
  });

  const [verificationResult, setVerificationResult] = useState<VerifyPaymentResponse | null>(null);

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      setFormData({
        transactionId: "",
        providerId: "",
      });
      setVerificationResult(null);
    }
  }, [isOpen]);

  const { mutate: submitVerification, isPending: isVerifying } = useMutation({
    mutationFn: ({ transactionId, providerId }: { transactionId: string; providerId: string }) =>
      verifyPayment(transactionId, providerId),
    onSuccess: (data) => {
      setVerificationResult(data);
      toast.success("Payment verification completed!");
      // Invalidate transactions query to refresh data
      queryClient.invalidateQueries({ queryKey: ["transactions"] });
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to verify payment"
      );
      console.error("Payment verification error:", error);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.transactionId.trim()) {
      toast.error("Please enter a transaction ID.");
      return;
    }

    if (!formData.providerId.trim()) {
      toast.error("Please enter a provider ID.");
      return;
    }

    submitVerification({
      transactionId: formData.transactionId.trim(),
      providerId: formData.providerId.trim(),
    });
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleClose = () => {
    if (!isVerifying) {
      onClose();
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "success":
      case "successful":
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "failed":
      case "declined":
      case "error":
        return <XCircle className="h-5 w-5 text-red-600" />;
      case "pending":
      case "processing":
        return <Clock className="h-5 w-5 text-yellow-600" />;
      default:
        return <AlertCircle className="h-5 w-5 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "success":
      case "successful":
      case "completed":
        return "bg-green-100 text-green-800 hover:bg-green-200";
      case "failed":
      case "declined":
      case "error":
        return "bg-red-100 text-red-800 hover:bg-red-200";
      case "pending":
      case "processing":
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
      default:
        return "bg-gray-100 text-gray-800 hover:bg-gray-200";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Verify Payment</DialogTitle>
          <DialogDescription>
            Enter the transaction ID and provider ID to verify payment status
          </DialogDescription>
        </DialogHeader>

        {!verificationResult ? (
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="transactionId" className="text-sm text-gray-500">
                TRANSACTION ID
              </Label>
              <Input
                id="transactionId"
                type="text"
                value={formData.transactionId}
                onChange={(e) => handleInputChange("transactionId", e.target.value)}
                placeholder="Enter transaction ID"
                className="bg-[#f9fafb] border-0 rounded-full"
                required
              />
            </div>

            <div>
              <Label htmlFor="providerId" className="text-sm text-gray-500">
                PROVIDER ID
              </Label>
              <Input
                id="providerId"
                type="text"
                value={formData.providerId}
                onChange={(e) => handleInputChange("providerId", e.target.value)}
                placeholder="Enter provider ID"
                className="bg-[#f9fafb] border-0 rounded-full"
                required
              />
            </div>

            <DialogFooter className="gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={handleClose}
                disabled={isVerifying}
                className="rounded-full"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isVerifying}
                className="bg-black hover:bg-gray-800 text-white rounded-full"
              >
                {isVerifying ? "Verifying..." : "Verify Payment"}
              </Button>
            </DialogFooter>
          </form>
        ) : (
          <div className="space-y-4">
            <div className="bg-gray-50 rounded-lg p-4 space-y-3">
              <h3 className="text-sm font-medium text-gray-700">Verification Results</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-xs text-gray-500">STATUS</Label>
                  <div className="flex items-center gap-2 mt-1">
                    {getStatusIcon(verificationResult.status)}
                    <Badge className={getStatusColor(verificationResult.status)}>
                      {verificationResult.status}
                    </Badge>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-gray-500">COMPLETED</Label>
                  <div className="flex items-center gap-2 mt-1">
                    {verificationResult.isCompleted ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="text-sm">
                      {verificationResult.isCompleted ? "Yes" : "No"}
                    </span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-gray-500">CREDIT TO WALLET</Label>
                  <div className="flex items-center gap-2 mt-1">
                    {verificationResult.isCreditToWallet ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : (
                      <XCircle className="h-4 w-4 text-red-600" />
                    )}
                    <span className="text-sm">
                      {verificationResult.isCreditToWallet ? "Yes" : "No"}
                    </span>
                  </div>
                </div>

                <div>
                  <Label className="text-xs text-gray-500">REFERENCE</Label>
                  <p className="text-sm font-mono bg-white px-2 py-1 rounded border mt-1 break-all">
                    {verificationResult.transaction_reference || "N/A"}
                  </p>
                </div>
              </div>
            </div>

            <DialogFooter className="gap-2 pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setVerificationResult(null)}
                className="rounded-full"
              >
                Verify Another
              </Button>
              <Button
                type="button"
                onClick={handleClose}
                className="bg-black hover:bg-gray-800 text-white rounded-full"
              >
                Close
              </Button>
            </DialogFooter>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
}
