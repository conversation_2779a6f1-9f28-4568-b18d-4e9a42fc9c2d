import React from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Transaction } from "@/types/transaction";
import { AnimatePresence, motion } from "motion/react";
import { MoreVertical } from "lucide-react";
import { Button } from "../ui/button";
import {
  <PERSON><PERSON><PERSON>,
  Too<PERSON>ip<PERSON>ontent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

const MotionTableRow = motion.create(TableRow);
interface TransactionTableProps {
  currentItems: Transaction[];
  getStatusColor: (status: string) => string;
}

const TransactionTable: React.FC<TransactionTableProps> = ({
  currentItems,
  getStatusColor,
}) => {
  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="text-xs font-medium text-gray-500">
              TRANSACTION ID
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              NAME
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              PHONE NO.
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              TYPE
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              WALLET
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              DATE
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              STATUS
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500"></TableHead>
          </TableRow>
        </TableHeader>
        <AnimatePresence mode="wait">
          <TableBody>
            {currentItems.map((transaction) => (
              <MotionTableRow
                key={transaction.id}
                initial={{ opacity: 0, y: 10 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                transition={{ duration: 0.3 }}
              >
                <TableCell className="w-[140px] max-w-[140px] truncate px-4 py-3 text-xs font-medium text-muted-foreground">
                  <TooltipProvider delayDuration={100}>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <span>{transaction.id}</span>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>{transaction.id}</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </TableCell>
                <TableCell>
                  <div>
                    <div className="text-sm font-medium">
                      {transaction.name}
                    </div>
                    <div className="text-xs text-gray-500">
                      {transaction.email}
                    </div>
                  </div>
                </TableCell>
                <TableCell className="text-xs">{transaction.phone}</TableCell>
                <TableCell>
                  <div>
                    <div className="text-xs">{transaction.type}</div>
                    <div className="text-xs text-gray-500">
                      {transaction.category}
                    </div>
                  </div>
                </TableCell>
                <TableCell className="text-xs font-medium">
                  {transaction.amount}
                </TableCell>
                <TableCell className="text-xs">{transaction.date}</TableCell>
                <TableCell>
                  <Badge
                    className={`${getStatusColor(
                      transaction.status
                    )} font-medium`}
                  >
                    {transaction.status}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button variant="ghost" size="icon" className="h-8 w-8">
                    <MoreVertical className="h-4 w-4" />
                  </Button>
                </TableCell>
              </MotionTableRow>
            ))}
          </TableBody>
        </AnimatePresence>
      </Table>
    </div>
  );
};

export default TransactionTable;
