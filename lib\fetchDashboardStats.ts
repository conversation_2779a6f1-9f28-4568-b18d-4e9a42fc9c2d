"use server";

import { cookies } from "next/headers";

export interface DashboardStats {
  total_count: number;
  total_users: number;
  total_requests: number;
}

export interface DashboardStatsResponse {
  message: string;
  result: DashboardStats;
  status: number;
}

export interface DashboardStatsParams {
  location_id?: number;
}

export async function fetchDashboardStats(
  params?: DashboardStatsParams
): Promise<DashboardStats> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  // Build URL with query parameters if provided
  const searchParams = new URLSearchParams();
  if (params?.location_id !== undefined) {
    searchParams.append("location_id", params.location_id.toString());
  }

  const queryString = searchParams.toString();
  const API_URL = `${API_BASE_URL}/admin/user-country-requests${
    queryString ? `?${queryString}` : ""
  }`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }

      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to fetch dashboard stats.";

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: DashboardStatsResponse = await response.json();

    if (data.message !== "success" || !data.result) {
      console.error("Unexpected API response structure:", data);
      throw new Error(
        "Failed to fetch dashboard stats or invalid data format."
      );
    }

    return data.result;
  } catch (error) {
    console.error("Error fetching or processing dashboard stats:", error);
    throw error;
  }
}
