"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import { fetchBranches } from "@/lib/fetchBranches";
import type { Branch } from "@/types/branch";
import { useAuth } from "@/store/auth-provider";

interface BranchContextType {
  selectedBranchId: number | null;
  setSelectedBranchId: (id: number | null) => void;
  selectedBranchName: string;
  availableBranches: Branch[];
  isLoading: boolean;
  error: unknown;
}

const BranchContext = createContext<BranchContextType | undefined>(undefined);

export function BranchProvider({ children }: { children: React.ReactNode }) {
  const { user } = useAuth();

  const [selectedBranchId, setSelectedBranchIdState] = useState<number | null>(
    () => {
      if (typeof window !== "undefined") {
        const savedBranchId = localStorage.getItem("selectedBranchId");
        return savedBranchId ? parseInt(savedBranchId, 10) : null;
      }
      return null;
    }
  );

  const [selectedBranchName, setSelectedBranchName] = useState<string>("");

  const setSelectedBranchId = (id: number | null) => {
    setSelectedBranchIdState(id);
    if (id !== null) {
      localStorage.setItem("selectedBranchId", id.toString());
    } else {
      localStorage.removeItem("selectedBranchId");
    }
  };

  const {
    data: branchesData,
    isLoading,
    error,
    refetch,
  } = useQuery<{ branches: Branch[] }>({
    queryKey: ["branches"],
    queryFn: fetchBranches,
    // retry: 3,
    // retryDelay: (attemptIndex) => Math.min(3000 * 2 ** attemptIndex, 10000),
  });

  useEffect(() => {
    if (user) {
      const timer = setTimeout(() => {
        refetch();
      }, 500);

      return () => clearTimeout(timer);
    }
  }, [user, refetch]);

  const availableBranches = React.useMemo(() => {
    if (!branchesData?.branches) return [];
    if (user?.role === "Admin") {
      return branchesData.branches;
    }

    return branchesData.branches.length > 0 ? [branchesData.branches[0]] : [];
  }, [branchesData?.branches, user]);

  // Update branch name when branch ID changes
  useEffect(() => {
    if (selectedBranchId && availableBranches.length > 0) {
      const branch = availableBranches.find((b) => b.id === selectedBranchId);
      if (branch) {
        setSelectedBranchName(branch.name);
      }
    } else {
      setSelectedBranchName("All Branches");
    }
  }, [selectedBranchId, availableBranches]);

  return (
    <BranchContext.Provider
      value={{
        selectedBranchId,
        setSelectedBranchId,
        selectedBranchName,
        availableBranches,
        isLoading,
        error,
      }}
    >
      {children}
    </BranchContext.Provider>
  );
}

export function useBranch() {
  const context = useContext(BranchContext);
  if (context === undefined) {
    throw new Error("useBranch must be used within a BranchProvider");
  }
  return context;
}
