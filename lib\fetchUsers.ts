// lib/fetchUsers.ts
"use server";

import { cookies } from "next/headers";
import { format, isValid, parseISO, getTime } from "date-fns";
import type {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  <PERSON>pi<PERSON>ser,
  UserApiResponse,
  FetchUsersParams,
  User,
} from "@/types/user";

// --- The Core Fetching Function ---
export async function fetchUsers(
  params: FetchUsersParams
): Promise<{ users: User[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;
  const API_URL = `${API_BASE_URL}/admin/members/all`;
  const searchParams = new URLSearchParams();

  // Append parameters if they exist and have valid values
  if (params.start) searchParams.append("start", params.start);
  if (params.end) searchParams.append("end", params.end);
  if (params.location_id !== undefined)
    searchParams.append("location_id", params.location_id.toString());
  if (params.export !== undefined)
    searchParams.append("export", params.export.toString());

  const fetchUrl = `${API_URL}?${searchParams.toString()}`;

  try {
    const response = await fetch(fetchUrl, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Check server logs for details.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: UserApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API response structure:", data);
      throw new Error("Failed to fetch users or invalid data format.");
    }

    // --- Map API data to UI structure ---
    const users: User[] = data.result.map((apiUser): User => {
      let formattedDate = "N/A";
      let registrationTimestamp = 0;
      try {
        if (apiUser.created_at) {
          const parsedDate = parseISO(apiUser.created_at);
          if (isValid(parsedDate)) {
            formattedDate = format(parsedDate, "dd/MM/yyyy");
            registrationTimestamp = getTime(parsedDate);
          } else {
            console.warn("Could not parse user date:", apiUser.created_at);
          }
        }
      } catch (e) {
        console.warn("Error parsing user date:", apiUser.created_at, e);
      }

      // Combine first and last name, handle nulls
      const name = `${apiUser.first_name || ""} ${
        apiUser.last_name || ""
      }`.trim();

      return {
        id: apiUser.id,
        name: name || "N/A",
        email: apiUser.email || "N/A",
        phone: apiUser.phone || "N/A",
        location: {
          city: apiUser.branch_state || apiUser.state || "N/A",
          state: apiUser.branch_state || apiUser.state || "N/A",
          address: apiUser.address || "N/A",
          country: apiUser.branch_country || apiUser.country || "N/A",
          branch: apiUser.branch || "N/A",
        },
        registeredOn: formattedDate,
        registrationTimestamp: registrationTimestamp,
        wallets: apiUser.wallets || [],
      };
    });

    return { users };
  } catch (error) {
    console.error("Error fetching or processing users:", error);
    throw error;
  }
}
