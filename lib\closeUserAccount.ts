"use server";

import { cookies } from "next/headers";

export interface CloseAccountResponse {
  message: string;
  result: string;
  status: number;
}

export async function closeUserAccount(
  userId: string
): Promise<CloseAccountResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  if (!API_BASE_URL) {
    throw new Error("API base URL is undefined. Check environment variables.");
  }

  const API_URL = `${API_BASE_URL}/admin/members/${userId}/close-account`;
  console.log("Closing user account at:", API_URL);

  try {
    const response = await fetch(API_URL, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`API Error Response (${response.status}):`, errorData);
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Check server logs for details."
        }`
      );
    }

    const data: CloseAccountResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error closing user account:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to close user account. Please try again later.");
  }
}
