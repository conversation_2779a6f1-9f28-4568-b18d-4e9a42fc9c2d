"use client";

import { RefreshCw } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useCallback, useEffect, useState, useMemo } from "react";
import { useAuth } from "@/store/auth-provider";
import { Skeleton } from "@/components/ui/skeleton";

export default function Navigation() {
  const pathname = usePathname();
  const [currentDate, setCurrentDate] = useState<string>("");
  const [currentTime, setCurrentTime] = useState<string>("");
  const { user, isLoading } = useAuth();

  const updateDateTime = useCallback(() => {
    const now = new Date();

    setCurrentDate(
      now.toLocaleDateString("en-GB", {
        day: "numeric",
        month: "long",
        year: "numeric",
      })
    );

    setCurrentTime(
      now.toLocaleTimeString("en-US", {
        hour: "numeric",
        minute: "2-digit",
        hour12: true,
      })
    );
  }, []);

  useEffect(() => {
    updateDateTime();
    const intervalId = setInterval(updateDateTime, 1000);

    return () => clearInterval(intervalId);
  }, [updateDateTime]);

  const handleRefresh = () => {
    updateDateTime();
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const allNavigationItems = [
    { name: "OVERVIEW", path: "/overview" },
    { name: "TRANSACTIONS", path: "/transactions" },
    { name: "USERS", path: "/users" },
    { name: "DECLARATION", path: "/declaration" },
    { name: "SUPPORT", path: "/support" },
  ];

  const navigationItems = useMemo(() => {
    // If still loading, return empty array to prevent flashing unauthorized items
    if (isLoading) {
      return [];
    }

    // If user is not a Support user, show all navigation items
    if (user && user.role !== "Support") {
      return allNavigationItems;
    }

    // For Support users or if user is null (safety check), only show DECLARATION and SUPPORT
    return allNavigationItems.filter(
      (item) => item.name === "DECLARATION" || item.name === "SUPPORT"
    );
  }, [user, allNavigationItems, isLoading]);

  return (
    <nav className="container mx-auto md:px-6 md:py-6 px-4 py-4 flex justify-between items-center pb-2 pt-8">
      <div className="flex space-x-8 text-black font-medium">
        {isLoading ? (
          <>
            <Skeleton className="h-6 w-20 rounded" />
            <Skeleton className="h-6 w-20 rounded" />
          </>
        ) : (
          // Show navigation items once loaded
          navigationItems.map((item) => (
            <Link
            prefetch
              key={item.name}
              href={item.path}
              className={`py-2 text-sm font-medium ${
                pathname === item.path
                  ? "border-b-2 border-black"
                  : "text-gray-500"
              }`}
            >
              {item.name}
            </Link>
          ))
        )}
      </div>

      <div className="flex space-x-4 text-sm">
        <span className="text-[#9C9A9A]">~ {currentDate}</span>
        <span className="mx-2 text-[#9C9A9A]">|</span>
        <span className="text-[#9C9A9A]">LAST UPDATE:</span>
        <span className="ml-1 text-black">{currentTime}</span>
        <button
          type="button"
          onClick={handleRefresh}
          aria-label="Refresh time"
          className="ml-1 p-1 rounded-full hover:bg-gray-100 active:scale-90 transition-all duration-150 ease-in-out" // Make it look like a button
        >
          <RefreshCw
            className="h-3 w-3 text-muted-foreground hover:text-foreground"
            aria-hidden="true"
          />
        </button>
      </div>
    </nav>
  );
}
