// components/transactions/TransactionHeader.tsx
"use client";

import type React from "react";
import { Download, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Popover, PopoverTrigger } from "@/components/ui/popover";
import { Filter } from "lucide-react";

interface TransactionHeaderProps {
  transactionCount: number;
  search: string | null;
  handleSearch: (e: React.ChangeEvent<HTMLInputElement>) => void;
  activeFilterCount: number;
  children: React.ReactNode;
  handleExport: () => void;
  isExportLoading?: boolean;
}

export default function TransactionHeader({
  transactionCount,
  search,
  handleSearch,
  activeFilterCount,
  handleExport,
  isExportLoading,
  children,
}: TransactionHeaderProps) {
  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6 gap-4">
      <div className="flex items-center space-x-4">
        <h2 className="text-xl font-bold">TRANSACTIONS</h2>
        <span className="text-sm text-gray-500">
          {transactionCount.toLocaleString()}
        </span>
      </div>

      <div className="flex flex-wrap items-center gap-3 w-full md:w-auto">
        {/* Search (Client-side) */}
        <div className="relative flex-grow md:flex-grow-0">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            type="text"
            placeholder="Search transactions..."
            className="pl-10 w-full md:w-[250px] shadow-none border-none bg-[#F1F1F1] rounded-full"
            value={search ?? ""}
            onChange={handleSearch}
          />
        </div>

        {/* Advanced Filters Button */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="rounded-full bg-[#F1F1F1] border-none relative"
            >
              <Filter className="h-4 w-4 mr-2" />
              <span>Filters</span>
              {activeFilterCount > 0 && (
                <Badge className="ml-2 bg-[#866cef] text-white h-5 w-5 p-0 flex items-center justify-center rounded-full">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>
          </PopoverTrigger>
          {children}
        </Popover>
        <Button
          className="flex items-center rounded-full"
          onClick={handleExport}
          disabled={isExportLoading}
        >
          <Download className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">Export</span>
        </Button>
        {/* Export Button - TODO: Implement export functionality */}
        {/* This might move or be handled differently later */}
        {/* <TooltipProvider> ... </TooltipProvider> */}
      </div>
    </div>
  );
}
