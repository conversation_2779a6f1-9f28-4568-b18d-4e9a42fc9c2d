"use client";

import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Transaction } from "@/types/transaction";

interface TransactionSummaryProps {
  transactions: Transaction[] | undefined;
  isLoading: boolean;
}

export default function TransactionSummary({
  transactions,
  isLoading,
}: TransactionSummaryProps) {
  const summaryData = React.useMemo(() => {
    const defaultData = {
      total: 0,
      totalNaira: 0,
      totalPounds: 0,
      totalEuro: 0,
      totalDollars: 0,
    };

    if (!transactions || transactions.length === 0) {
      return defaultData;
    }

    // Initialize totals
    let totalNaira = 0;
    let totalPounds = 0;
    let totalEuro = 0;
    let totalDollars = 0;

    transactions.forEach((transaction) => {
      const amount = transaction.amountValue;

      // Determine currency based on the currency code
      if (transaction.currency === "NGN") {
        totalNaira += amount;
      } else if (transaction.currency === "GBP") {
        totalPounds += amount;
      } else if (transaction.currency === "EUR") {
        totalEuro += amount;
      } else if (transaction.currency === "USD") {
        totalDollars += amount;
      }
    });

    return {
      total: transactions.length,
      totalNaira,
      totalPounds,
      totalEuro,
      totalDollars,
    };
  }, [transactions]);

  // Format currency values
  const formatCurrency = (value: number, currency: string) => {
    const formatter = new Intl.NumberFormat("en", {
      style: "currency",
      currency,
      minimumFractionDigits: 2,
    });
    return formatter.format(value);
  };

  if (isLoading) {
    return (
      <Card className="mb-6">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
            <div className="space-y-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-32" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-32" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-32" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-32" />
            </div>
            <div className="space-y-2">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-8 w-32" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="mb-6">
      <CardContent className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <div>
            <h3 className="text-sm font-medium text-muted-foreground uppercase">
              TOTAL
            </h3>
            <p className="text-2xl font-bold">{summaryData.total}</p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground uppercase">
              AMOUNT (NGN)
            </h3>
            <p className="text-2xl font-bold">
              ₦{summaryData.totalNaira.toLocaleString()}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground uppercase">
              AMOUNT (GBP)
            </h3>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryData.totalPounds, "GBP")}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground uppercase">
              AMOUNT (EUR)
            </h3>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryData.totalEuro, "EUR")}
            </p>
          </div>
          <div>
            <h3 className="text-sm font-medium text-muted-foreground uppercase">
              AMOUNT (USD)
            </h3>
            <p className="text-2xl font-bold">
              {formatCurrency(summaryData.totalDollars, "USD")}
            </p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
