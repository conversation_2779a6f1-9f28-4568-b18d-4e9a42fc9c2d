"use server";

import { cookies } from "next/headers";

export interface BankAccount {
  account_name: string;
  account_number: string;
  bank_name: string;
  branch_id: number;
  currency: string;
  id: string;
  payment_type: string;
}

export interface BankAccountsResponse {
  result: BankAccount[];
  message: string;
  status: number;
}

export interface BankAccountParams {
  branchid?: string;
  currency?: string;
  giving_type?: string;
}

export async function fetchBankAccounts(
  params?: BankAccountParams
): Promise<BankAccountsResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  // Build URL with query parameters if provided
  const searchParams = new URLSearchParams();
  if (params?.branchid) {
    searchParams.append("branchid", params.branchid);
  }
  if (params?.currency) {
    searchParams.append("currency", params.currency);
  }
  if (params?.giving_type) {
    searchParams.append("giving_type", params.giving_type);
  }

  const queryString = searchParams.toString();
  const API_URL = `${API_BASE_URL}/admin/bank-accounts${
    queryString ? `?${queryString}` : ""
  }`;

  try {
    const response = await fetch(API_URL, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`API Error Response (${response.status}):`, errorData);
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Check server logs for details."
        }`
      );
    }

    const data = await response.json();
    return {
      result: data.result || [],
      message: data.message || "Bank accounts fetched successfully",
      status: data.status || 200,
    };
  } catch (error) {
    console.error("Error fetching bank accounts:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to fetch bank accounts. Please try again later.");
  }
}

export interface NewBankAccountPayload {
  account_name: string;
  account_number: string;
  bank_name: string;
  branch_id: number;
  currency: string;
  payment_type: string;
}

export interface BankAccountResponse {
  message: string;
  status: number;
}

export async function addBankAccount(
  payload: NewBankAccountPayload
): Promise<BankAccountResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/accounts/new`;

  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`API Error Response (${response.status}):`, errorData);
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Check server logs for details."
        }`
      );
    }

    const data = await response.json();
    return {
      message: data.message || "Bank account added successfully",
      status: 200,
    };
  } catch (error) {
    console.error("Error adding bank account:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to add bank account. Please try again later.");
  }
}

export interface UpdateBankAccountPayload {
  account_name?: string;
  account_number?: string;
  bank_name?: string;
  branch_id?: number;
  currency?: string;
  payment_type?: string;
}

export async function updateBankAccount(
  id: string,
  payload: UpdateBankAccountPayload
): Promise<BankAccountResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/accounts/${id}/update`;

  try {
    const response = await fetch(API_URL, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`API Error Response (${response.status}):`, errorData);
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Check server logs for details."
        }`
      );
    }

    const data = await response.json();
    return {
      message: data.message || "Bank account updated successfully",
      status: 200,
    };
  } catch (error) {
    console.error("Error updating bank account:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to update bank account. Please try again later.");
  }
}
