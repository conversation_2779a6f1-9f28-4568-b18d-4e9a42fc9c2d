"use server";

import { cookies } from "next/headers";

export async function exportTransactions() {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const BASE_URL = process.env.BASE_URL;

  if (!token) {
    throw new Error("Authentication token not found");
  }

  const response = await fetch(
    `${BASE_URL}/admin/transactions/all?export=true`,
    {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    }
  );

  if (!response.ok) {
    throw new Error("Failed to export transactions");
  }

  const blob = await response.blob();
  return blob;
}
