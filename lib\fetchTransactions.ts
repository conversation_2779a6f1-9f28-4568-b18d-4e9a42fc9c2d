// lib/fetchTransactions.ts (or api/transactions.ts)
"use server";
import {
  ApiResponse,
  FetchTransactionsParams,
  Transaction,
} from "@/types/transaction";
import { format, isValid, parseISO } from "date-fns";
import { cookies } from "next/headers";

export async function fetchTransactions(
  params: FetchTransactionsParams
): Promise<{ transactions: Transaction[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const searchParams = new URLSearchParams();

  if (params.start) searchParams.append("start", params.start);
  if (params.end) searchParams.append("end", params.end);
  if (params.price_min !== undefined)
    searchParams.append("price_min", params.price_min.toString());
  if (params.price_max !== undefined)
    searchParams.append("price_max", params.price_max.toString());
  if (params.export !== undefined)
    searchParams.append("export", params.export.toString());
  if (params.member_id !== undefined)
    searchParams.append("member_id", params.member_id.toString());
  if (params.period) searchParams.append("period", params.period);
  if (params.location_id !== undefined)
    searchParams.append("location_id", params.location_id.toString());
  if (params.currency) searchParams.append("currency", params.currency);

  try {
    const response = await fetch(
      `${
        process.env.BASE_URL
      }/admin/transactions/all?${searchParams.toString()}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
          accept: "application/json",
        },
        cache: "no-store",
      }
    );

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error(`API Error Response (${response.status}):`, errorData);
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          errorData?.message || "Check server logs for details."
        }`
      );
    }

    const data: ApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API response structure:", data);
      throw new Error("Failed to fetch transactions or invalid data format.");
    }

    const transactions: Transaction[] = data.result.map((tx, index) => {
      const amountValue = parseFloat(tx.transaction_amount) || 0;

      let formattedDate = "N/A";
      try {
        if (tx.transaction_date) {
          const parsedDate = parseISO(tx.transaction_date);
          if (isValid(parsedDate)) {
            formattedDate = format(parsedDate, "dd/MM/yyyy");
          } else {
            console.warn("Could not parse date:", tx.transaction_date);
          }
        }
      } catch (e) {
        console.warn("Error parsing date:", tx.transaction_date, e);
      }

      const transactionSystemType = tx.payment_mode || tx.provider || "Unknown";

      const transactionCategory = tx.transaction_type_string || "General";
      const transactionStatus =
        tx.transaction_status_string?.toUpperCase() || "UNKNOWN";
      const keyId =
        tx.transaction_id ||
        (tx.id !== null && tx.id !== undefined
          ? `id-${tx.id.toString()}`
          : null) ||
        `generated-${index}`;

      // Determine currency and symbol
      let currencySymbol = "₦";
      let currencyCode = "NGN";

      switch (tx.currency) {
        case "EUR":
          currencySymbol = "€";
          currencyCode = "EUR";
          break;
        case "GBP":
          currencySymbol = "£";
          currencyCode = "GBP";
          break;
        case "USD":
          currencySymbol = "$";
          currencyCode = "USD";
          break;
        default:
          currencySymbol = "₦";
          currencyCode = "NGN";
      }

      const formattedAmount = `${currencySymbol}${amountValue.toLocaleString(
        currencyCode === "EUR" ? "de-DE" : "en-NG",
        { minimumFractionDigits: 2, maximumFractionDigits: 2 }
      )}`;

      return {
        id: keyId,
        name: `${tx.first_name || ""} ${tx.last_name || ""}`.trim() || "N/A",
        email: tx.email || "N/A",
        phone: tx.phone || "N/A",
        type: transactionSystemType,
        category: transactionCategory,
        amount: formattedAmount,
        amountValue: amountValue,
        currency: currencyCode,
        date: formattedDate,
        status: transactionStatus,
      };
    });

    return { transactions };
  } catch (error) {
    console.error("Error fetching or processing transactions:", error);
    throw new Error("Failed to fetch transactions. Please try again later.");
  }
}
