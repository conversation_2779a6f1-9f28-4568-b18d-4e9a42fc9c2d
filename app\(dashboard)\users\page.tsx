// app/(dashboard)/users/page.tsx (Server Component)
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { fetchUsers } from "@/lib/fetchUsers";
import { getQueryClient } from "@/store/get-query-client";
import type { FetchUsersParams } from "@/types/user";
import { fetchGlobalDistribution } from "@/lib/fetchGlobalDistribution";
import { fetchUserStats } from "@/lib/fetchUserStats";
import UsersClientPage from "@/components/users/users";
type SearchParams = {
  searchParams: Promise<{
    [key: string]: string | string[] | undefined;
  }>;
};

export default async function UsersPage({ searchParams }: SearchParams) {
  const queryClient = getQueryClient();

  const resolvedParams = await searchParams;

  const initialQuery = resolvedParams.q as string | undefined;
  const initialStartDate = resolvedParams.start as string | undefined;
  const initialEndDate = resolvedParams.end as string | undefined;
  const initialLocationId = resolvedParams.location_id
    ? parseInt(resolvedParams.location_id as string, 10)
    : undefined;
  const initialExport = resolvedParams.export === "true";

  const initialFetchParams: FetchUsersParams = {
    start: initialStartDate,
    end: initialEndDate,
    location_id: initialLocationId,
    export: initialExport || undefined,
  };

  const usersQueryKey = ["users", initialFetchParams];
  const globalDistQueryKey = ["globalDistribution"];
  const userStatsQueryKey = ["userStats"];

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: globalDistQueryKey,
      queryFn: () => fetchGlobalDistribution(),
    }),
    queryClient.prefetchQuery({
      queryKey: usersQueryKey,
      queryFn: () => fetchUsers(initialFetchParams),
    }),
    queryClient.prefetchQuery({
      queryKey: userStatsQueryKey,
      queryFn: () => fetchUserStats(),
    }),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <UsersClientPage initialSearch={initialQuery} />
    </HydrationBoundary>
  );
}
