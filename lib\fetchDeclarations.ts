"use server";

import { cookies } from "next/headers";
import type {
  Declaration,
  NewDeclarationPayload,
  UpdateDeclarationPayload,
  DeleteDeclarationResponse,
} from "@/types/declaration"; // Added DeleteDeclarationResponse

interface DeclarationsApiResponse {
  message: string;
  result: Declaration[];
  status: number;
}

interface CreateDeclarationApiResponse {
  message: string;
  result: string;
  status: number;
}

const API_BASE_URL = process.env.BASE_URL;

// --- Fetch All Declarations ---
export async function fetchDeclarations(): Promise<{
  declarations: Declaration[];
}> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/declarations`;
  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to fetch declarations.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: DeclarationsApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API response structure:", data);
      throw new Error("Failed to fetch declarations or invalid data format.");
    }

    return { declarations: data.result };
  } catch (error) {
    console.error("Error fetching or processing declarations:", error);
    throw error;
  }
}

// --- Create New Declaration ---
export async function createDeclaration(
  payload: NewDeclarationPayload
): Promise<{ success: boolean; message: string }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/declarations/new`;
  console.log("Creating declaration at:", API_URL);

  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to create declaration.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: CreateDeclarationApiResponse = await response.json();

    if (data.message === "success" && data.status === 200) {
      return { success: true, message: data.message };
    } else {
      console.error("Unexpected API response structure on create:", data);
      throw new Error(
        data.message ||
          "Failed to create declaration or invalid response format."
      );
    }
  } catch (error) {
    console.error("Error creating declaration:", error);
    throw error;
  }
}

// --- Update Declaration ---
export async function updateDeclaration(
  id: number,
  payload: UpdateDeclarationPayload
): Promise<{ success: boolean; message: string; declaration?: Declaration }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/declarations/${id}/update`;

  try {
    const response = await fetch(API_URL, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to update declaration.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }
    const data = await response.json();

    if (data.message === "success" && data.status === 200) {
      return { success: true, message: data.message, declaration: data.result };
    } else {
      console.error("Unexpected API response structure on update:", data);
      throw new Error(
        data.message ||
          "Failed to update declaration or invalid response format."
      );
    }
  } catch (error) {
    console.error(`Error updating declaration ${id}:`, error);
    throw error;
  }
}

// --- Delete Declaration ---
export async function deleteDeclaration(
  id: number
): Promise<{ success: boolean; message: string }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/declarations/${id}/delete`;
  console.log(`Deleting declaration ${id} at:`, API_URL);

  try {
    const response = await fetch(API_URL, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to delete declaration.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: DeleteDeclarationResponse = await response.json();

    if (data.message === "success" && data.status === 200) {
      return { success: true, message: "Declaration deleted successfully" };
    } else {
      console.error("Unexpected API response structure on delete:", data);
      throw new Error(
        data.message ||
          "Failed to delete declaration or invalid response format."
      );
    }
  } catch (error) {
    console.error(`Error deleting declaration ${id}:`, error);
    throw error;
  }
}
