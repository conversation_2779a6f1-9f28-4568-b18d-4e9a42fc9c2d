// app/(dashboard)/users/[id]/page.tsx (Server Component)
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { fetchUserById } from "@/lib/fetchUserById";
import { prefetchUserData } from "@/lib/prefetchUserData";
import { notFound } from "next/navigation";
import UserPreviewClientPage from "@/components/users/user-preview";

type UserPreviewParams = {
  params: Promise<{
    id: string;
  }>;
};

export default async function UserPreviewPage({ params }: UserPreviewParams) {
  const userId = (await params).id;

  // Prefetch all user data
  const queryClient = await prefetchUserData(userId);

  // Fetch user data again to pass as initial prop (could potentially get from cache, but following existing pattern)
  // Error handling for user fetch is important here
  let user;
  try {
    const userData = await fetchUserById(userId);
    user = userData.user;
  } catch (error) {
    console.error("Failed to fetch user data for preview page:", error);
    // Handle error appropriately, maybe show a generic error page or redirect
    // For now, we'll let the notFound() below handle it if user is null/undefined
  }

  if (!user) {
    notFound();
  }

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <UserPreviewClientPage initialUser={user} userId={userId} />
    </HydrationBoundary>
  );
}
