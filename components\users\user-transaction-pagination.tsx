"use client";

import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import React from "react";

interface UserTransactionPaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
}

export default function UserTransactionPagination({
  currentPage,
  totalPages,
  onPageChange,
}: UserTransactionPaginationProps) {
  const getPaginationItems = (): React.ReactNode[] => {
    const items: React.ReactNode[] = [];
    const maxVisiblePages = 5;

    if (totalPages <= 1) return items;

    // Always show first page
    items.push(
      <Button
        key={1}
        variant={currentPage === 1 ? "default" : "outline"}
        size="icon"
        className={`h-8 w-8 rounded-full ${
          currentPage === 1 ? "bg-[#866cef]" : ""
        }`}
        onClick={() => onPageChange(1)}
      >
        1
      </Button>
    );

    const startPage = Math.max(
      2,
      currentPage - Math.floor(maxVisiblePages / 2)
    );
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

    if (startPage > 2) {
      items.push(
        <span key="ellipsis1" className="px-1">
          ...
        </span>
      );
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <Button
          key={i}
          variant={currentPage === i ? "default" : "outline"}
          size="icon"
          className={`h-8 w-8 rounded-full ${
            currentPage === i ? "bg-[#866cef]" : ""
          }`}
          onClick={() => onPageChange(i)}
        >
          {i}
        </Button>
      );
    }

    if (endPage < totalPages - 1) {
      items.push(
        <span key="ellipsis2" className="px-1">
          ...
        </span>
      );
    }

    // Always show last page
    if (totalPages > 1) {
      items.push(
        <Button
          key={totalPages}
          variant={currentPage === totalPages ? "default" : "outline"}
          size="icon"
          className={`h-8 w-8 rounded-full ${
            currentPage === totalPages ? "bg-[#866cef]" : ""
          }`}
          onClick={() => onPageChange(totalPages)}
        >
          {totalPages}
        </Button>
      );
    }

    return items;
  };

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="flex justify-center items-center mt-6 space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(Math.max(1, currentPage - 1))}
        disabled={currentPage === 1}
        className="flex items-center"
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        Previous
      </Button>
      <div className="flex items-center space-x-1">{getPaginationItems()}</div>
      <Button
        variant="outline"
        size="sm"
        onClick={() => onPageChange(Math.min(totalPages, currentPage + 1))}
        disabled={currentPage === totalPages}
        className="flex items-center"
      >
        Next
        <ChevronRight className="h-4 w-4 ml-1" />
      </Button>
    </div>
  );
}
