"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Globe,
  Copy,
  Check,
  Loader2,
  Edit2,
  MoreVertical,
  Eye,
  EyeOff,
} from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Dialog,
  DialogDescription,
  DialogFooter,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { useAuth } from "@/store/auth-provider";
import { fetchBranches, addBranch, updateBranch } from "@/lib/fetchBranches";
import { fetchCountries } from "@/lib/fetchCountries";
import type {
  Branch,
  NewBranchPayload,
  UpdateBranchPayload,
} from "@/types/branch";
import type { Country } from "@/types/country";
import type { Manager, UpdateManagerPayload } from "@/types/manager";
import { fetchAllManagers, updateManager } from "@/lib/fetchManagers";
import { registerUser, RegisterUserPayload } from "@/lib/registerUser";
import {
  fetchBankAccounts,
  addBankAccount,
  updateBankAccount,
  BankAccount,
  NewBankAccountPayload,
  UpdateBankAccountPayload,
} from "@/lib/fetchBankAccounts";
import { Skeleton } from "@/components/ui/skeleton";
import { useBranch } from "@/store/branch-provider";

type LocationFormState = {
  country: string;
  state: string;
  city: string;
  address: string;
  name: string;
};

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("profile");
  const [activeAdminTab, setActiveAdminTab] = useState("all-users");
  const [showAddUserDialog, setShowAddUserDialog] = useState(false);
  const [showAddLocationDialog, setShowAddLocationDialog] = useState(false);
  const [showEditManagerDialog, setShowEditManagerDialog] = useState(false);
  const [editingBranch, setEditingBranch] = useState<Branch | null>(null);
  const [editingManager, setEditingManager] = useState<Manager | null>(null);
  const [copied, setCopied] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const { user, logout } = useAuth();

  const queryClient = useQueryClient();

  // State for the New User form
  const [newUserForm, setNewUserForm] = useState<RegisterUserPayload>({
    branches: [],
    email: "",
    first_name: "",
    last_name: "",
    password: "",
    phone: "",
    role_name: "Admin",
  });

  // Get branch data from branch provider
  const { selectedBranchId: globalBranchId, availableBranches } = useBranch();

  // State for the Edit Manager form
  const [managerForm, setManagerForm] = useState<Partial<UpdateManagerPayload>>(
    {}
  );

  // State for the Bank Account form
  const [bankAccountForm, setBankAccountForm] = useState<NewBankAccountPayload>(
    {
      account_name: "",
      account_number: "",
      bank_name: "",
      branch_id: globalBranchId || 0,
      currency: "NGN",
      payment_type: "ProphetOffering",
    }
  );

  // State for editing a bank account
  const [editingBankAccount, setEditingBankAccount] =
    useState<BankAccount | null>(null);
  const [showAddBankAccountDialog, setShowAddBankAccountDialog] =
    useState(false); // Using Partial for flexibility

  // State for the Add/Edit Location form
  const [locationForm, setLocationForm] = useState<LocationFormState>({
    country: "",
    state: "",
    city: "",
    address: "",
    name: "",
  });

  // --- Fetch Countries Query ---
  const {
    data: countriesData,
    isLoading: isLoadingCountries,
    error: countriesError,
  } = useQuery<Country[]>({
    queryKey: ["countries"],
    queryFn: fetchCountries,
    enabled: activeTab === "locations" || showAddLocationDialog,
  });

  // --- Fetch Branches Query ---
  const {
    data: branchesData,
    isLoading: isLoadingBranches,
    error: branchesError,
    refetch: refetchBranches,
  } = useQuery<{ branches: Branch[] }>({
    queryKey: ["branches"],
    queryFn: fetchBranches,
    enabled: activeTab === "locations" || activeTab === "profile",
  });

  // --- Add Branch Mutation ---
  const { mutate: submitAddBranch, isPending: isAddingBranch } = useMutation({
    mutationFn: addBranch,
    onSuccess: (data) => {
      toast.success(data.message || "Branch added successfully!");
      queryClient.invalidateQueries({ queryKey: ["branches"] });
      setShowAddLocationDialog(false);
      // Reset form
      setLocationForm({
        country: "",
        state: "",
        city: "",
        address: "",
        name: "",
      });
    },
    onError: (error) => {
      console.error("Add branch error:", error);
      toast.error(error.message || "Failed to add branch.");
    },
  });

  // --- Update Branch Mutation ---
  const { mutate: submitUpdateBranch, isPending: isUpdatingBranch } =
    useMutation({
      mutationFn: ({
        id,
        payload,
      }: {
        id: number;
        payload: UpdateBranchPayload;
      }) => updateBranch(id, payload),
      onSuccess: (data) => {
        toast.success(data.message || "Branch updated successfully!");
        queryClient.invalidateQueries({ queryKey: ["branches"] });
        setShowAddLocationDialog(false);
        setEditingBranch(null);
      },
      onError: (error) => {
        console.error("Update branch error:", error);
        toast.error(error.message || "Failed to update branch.");
      },
    });

  // --- Fetch Managers Query ---
  const {
    data: managersData,
    isLoading: isLoadingManagers,
    error: managersError,
    refetch: refetchManagers,
  } = useQuery<{ managers: Manager[] }>({
    queryKey: ["managers"],
    queryFn: fetchAllManagers,
    enabled: activeTab === "administration" && activeAdminTab === "all-users",
  });

  // --- Fetch Bank Accounts Query ---
  const {
    data: bankAccountsData,
    isLoading: isLoadingBankAccounts,
    error: bankAccountsError,
    refetch: refetchBankAccounts,
  } = useQuery({
    queryKey: ["bankAccounts", globalBranchId],
    queryFn: () =>
      fetchBankAccounts({
        branchid:
          globalBranchId !== null ? globalBranchId.toString() : undefined,
      }),
    enabled: activeTab === "bankAccount",
  });

  // --- Add Bank Account Mutation ---
  const { mutate: submitAddBankAccount, isPending: isAddingBankAccount } =
    useMutation({
      mutationFn: addBankAccount,
      onSuccess: (data) => {
        toast.success(data.message || "Bank account added successfully!");
        queryClient.invalidateQueries({ queryKey: ["bankAccounts"] });
        setShowAddBankAccountDialog(false);
        // Reset form
        setBankAccountForm({
          account_name: "",
          account_number: "",
          bank_name: "",
          branch_id: 0,
          currency: "NGN",
          payment_type: "ProphetOffering",
        });
      },
      onError: (error) => {
        console.error("Add bank account error:", error);
        toast.error(error.message || "Failed to add bank account.");
      },
    });

  // --- Update Bank Account Mutation ---
  const { mutate: submitUpdateBankAccount, isPending: isUpdatingBankAccount } =
    useMutation({
      mutationFn: ({
        id,
        payload,
      }: {
        id: string;
        payload: UpdateBankAccountPayload;
      }) => updateBankAccount(id, payload),
      onSuccess: (data) => {
        toast.success(data.message || "Bank account updated successfully!");
        queryClient.invalidateQueries({ queryKey: ["bankAccounts"] });
        setShowAddBankAccountDialog(false);
        setEditingBankAccount(null);
      },
      onError: (error) => {
        console.error("Update bank account error:", error);
        toast.error(error.message || "Failed to update bank account.");
      },
    });

  // --- Register User Mutation ---
  const { mutate: submitRegisterUser, isPending: isRegisteringUser } =
    useMutation({
      mutationFn: (payload: RegisterUserPayload) => registerUser(payload),
      onSuccess: (data) => {
        toast.success(data.message || "User registered successfully!");
        queryClient.invalidateQueries({ queryKey: ["managers"] });
        setShowAddUserDialog(false);
        // Reset form
        setNewUserForm({
          branches: [],
          email: "",
          first_name: "",
          last_name: "",
          password: "",
          phone: "",
          role_name: "Admin",
        });
        setShowPassword(false);
      },
      onError: (error) => {
        console.error("Register user error:", error);
        toast.error(error.message || "Failed to register user.");
      },
    });

  // --- Update Manager Mutation ---
  const { mutate: submitUpdateManager, isPending: isUpdatingManager } =
    useMutation({
      mutationFn: ({
        id,
        payload,
      }: {
        id: string;
        payload: UpdateManagerPayload;
      }) => updateManager(id, payload),
      onSuccess: (data) => {
        toast.success(data.message || "Manager updated successfully!");
        queryClient.invalidateQueries({ queryKey: ["managers"] });
        setShowEditManagerDialog(false);
        setEditingManager(null);
        setManagerForm({});
      },
      onError: (error) => {
        console.error("Update manager error:", error);
        toast.error(error.message || "Failed to update manager.");
      },
    });

  // Handle input changes for the new user form
  const handleNewUserFormChange = (
    field: keyof RegisterUserPayload,
    value: string | number[]
  ) => {
    // Type guard to ensure correct type assignment
    if (field === "branches" && Array.isArray(value)) {
      // Handle branches field separately as it expects number[]
      setNewUserForm((prev) => ({ ...prev, [field]: value }));
    } else if (typeof value === "string") {
      // Handle string fields
      setNewUserForm((prev) => ({ ...prev, [field]: value }));
    }
  };

  // Handle input changes for the bank account form
  const handleBankAccountFormChange = (
    field: keyof NewBankAccountPayload,
    value: string | number
  ) => {
    setBankAccountForm((prev) => ({ ...prev, [field]: value }));
  };

  // Handle Select changes for the bank account form
  const handleBankAccountSelectChange = (
    field: keyof NewBankAccountPayload,
    value: string | number
  ) => {
    setBankAccountForm((prev) => ({ ...prev, [field]: value }));
  };

  // Handle input changes for the location form
  const handleLocationFormChange = (
    field: keyof LocationFormState,
    value: string
  ) => {
    setLocationForm((prev) => ({ ...prev, [field]: value }));
  };

  // Handle Select changes for the location form
  const handleLocationSelectChange = (
    field: keyof LocationFormState,
    value: string
  ) => {
    // The Select component passes the value directly
    setLocationForm((prev) => ({ ...prev, [field]: value }));
  };

  // Handle Add/Edit Location form submission
  const handleLocationSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Basic validation (remains the same)
    if (
      !locationForm.name ||
      !locationForm.address ||
      !locationForm.city ||
      !locationForm.state ||
      !locationForm.country
    ) {
      toast.error("Please fill in all location fields.");
      return;
    }

    // Role check - Allow 'Admin' or 'Superadmin'
    const userRoleUpper = user?.role?.toUpperCase();
    if (userRoleUpper !== "ADMIN" && userRoleUpper !== "SUPERADMIN") {
      toast.error("You do not have permission to add locations.");
      return;
    }

    const payload: NewBranchPayload | UpdateBranchPayload = {
      name: locationForm.name,
      address: locationForm.address,
      city: locationForm.city,
      state: locationForm.state,
      country: locationForm.country,
    };

    if (editingBranch) {
      // Update existing branch
      submitUpdateBranch({ id: editingBranch.id, payload });
    } else {
      // Add new branch
      submitAddBranch(payload);
    }
  };

  // Function to open the dialog for editing
  const handleEditClick = useCallback((branch: Branch) => {
    setEditingBranch(branch);
    setLocationForm({
      // Pre-fill form
      name: branch.name,
      address: branch.address,
      city: branch.city,
      state: branch.state,
      country: branch.country,
    });
    setShowAddLocationDialog(true);
  }, []);

  // Function to handle closing the dialog (reset state)
  const handleDialogClose = useCallback((open: boolean) => {
    if (!open) {
      setEditingBranch(null);
      setLocationForm({
        // Reset form
        country: "",
        state: "",
        city: "",
        address: "",
        name: "",
      });
    }
    setShowAddLocationDialog(open);
  }, []);

  // --- Edit Manager Dialog Handlers ---

  // Handle input changes for the manager form
  const handleManagerFormChange = (
    field: keyof UpdateManagerPayload,
    value: string | number[]
  ) => {
    // Type guard to ensure correct type assignment
    if (field === "branches" && Array.isArray(value)) {
      // Handle branches field separately as it expects number[]
      setManagerForm((prev) => ({ ...prev, [field]: value }));
    } else if (typeof value === "string") {
      // Handle string fields
      setManagerForm((prev) => ({ ...prev, [field]: value }));
    }
  };

  // Handle Select changes for the manager form
  const handleManagerSelectChange = (
    field: keyof UpdateManagerPayload,
    value: string | number[]
  ) => {
    // Type guard to ensure correct type assignment
    if (field === "branches" && Array.isArray(value)) {
      // Handle branches field separately as it expects number[]
      setManagerForm((prev) => ({ ...prev, [field]: value }));
    } else if (typeof value === "string") {
      // Handle string fields
      setManagerForm((prev) => ({ ...prev, [field]: value }));
    }
  };

  // Function to open the dialog for editing a manager
  const handleEditManagerClick = useCallback((manager: Manager) => {
    setEditingManager(manager);
    // Pre-fill form with existing manager data
    setManagerForm({
      first_name: manager.first_name,
      last_name: manager.last_name,
      email: manager.email,
      phone: manager.phone,
      address: manager.address,
      state: manager.state,
      country: manager.country,
      role_name: manager.role, // Use role_name to match API expectations
    });
    console.log("Setting manager form with role:", manager.role);
    setShowEditManagerDialog(true);
  }, []);

  // Handle Edit Manager form submission
  const handleManagerSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingManager) return;

    // Basic validation (add more as needed)
    if (!managerForm.first_name || !managerForm.last_name) {
      toast.error(
        "Please fill in required manager fields (First Name, Last Name)."
      );
      return;
    }

    // Role check - Allow 'Admin' or 'Superadmin' to update
    const userRoleUpper = user?.role?.toUpperCase();
    if (userRoleUpper !== "ADMIN") {
      toast.error("You do not have permission to update managers.");
      return;
    }

    // Construct payload. Include Id as required by the backend.
    const payload: UpdateManagerPayload = {
      Id: editingManager.id, // Always include the Id
    };

    // Add branch assignment using global branch ID
    if (globalBranchId) {
      payload.branches = [globalBranchId];
    }

    // Include all form fields in the payload
    if (managerForm.first_name) payload.first_name = managerForm.first_name;
    if (managerForm.last_name) payload.last_name = managerForm.last_name;
    if (managerForm.phone) payload.phone = managerForm.phone;

    // Ensure role_name is included in the payload
    if (managerForm.role_name) {
      payload.role_name = managerForm.role_name;
      console.log("Setting role_name in payload:", managerForm.role_name);
    }

    // If no changes and no branch assignment, maybe just close the dialog
    if (Object.keys(payload).length <= 1 && !payload.branches) {
      // Only Id is present
      setShowEditManagerDialog(false);
      setEditingManager(null);
      setManagerForm({});
      // toast.info("No changes detected."); // Optional feedback
      return;
    }

    console.log("Submitting manager update with payload:", payload);
    submitUpdateManager({ id: editingManager.id, payload });
  };

  // Function to handle closing the Edit Manager dialog (reset state)
  const handleEditManagerDialogClose = useCallback((open: boolean) => {
    if (!open) {
      setEditingManager(null);
      setManagerForm({}); // Reset form
    }
    setShowEditManagerDialog(open);
  }, []);

  // Handle new user form submission
  const handleNewUserSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (
      !newUserForm.first_name ||
      !newUserForm.last_name ||
      !newUserForm.email ||
      !newUserForm.password ||
      !newUserForm.phone ||
      !newUserForm.role_name
    ) {
      toast.error("Please fill in all required fields.");
      return;
    }

    // Role check - Allow 'Admin' or 'Superadmin'
    const userRoleUpper = user?.role?.toUpperCase();
    if (userRoleUpper !== "ADMIN" && userRoleUpper !== "SUPERADMIN") {
      toast.error("You do not have permission to add users.");
      return;
    }

    // Use the global branch ID for the new user
    if (globalBranchId) {
      // Create a copy of the form with the global branch ID
      const formWithBranch = {
        ...newUserForm,
        branches: [globalBranchId],
      };

      // Submit the form with the global branch ID
      submitRegisterUser(formWithBranch);
    } else {
      toast.error(
        "No branch selected. Please select a branch from the global branch selector."
      );
    }
  };

  // Handle Add User dialog close
  const handleAddUserDialogClose = useCallback((open: boolean) => {
    if (!open) {
      // Reset form when closing
      setNewUserForm({
        branches: [],
        email: "",
        first_name: "",
        last_name: "",
        password: "",
        phone: "",
        role_name: "Admin",
      });
      setShowPassword(false);
    }
    setShowAddUserDialog(open);
  }, []);

  // Handle bank account form submission
  const handleBankAccountSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Ensure branch_id is set to the global branch ID and payment_type is valid
    const formWithBranchId = {
      ...bankAccountForm,
      branch_id: globalBranchId || 0,
      currency: bankAccountForm.currency || "NGN",
      payment_type: bankAccountForm.payment_type || "ProphetOffering",
    };

    // Basic validation
    if (
      !formWithBranchId.account_name ||
      !formWithBranchId.account_number ||
      !formWithBranchId.bank_name ||
      !formWithBranchId.branch_id ||
      !formWithBranchId.currency ||
      !formWithBranchId.payment_type
    ) {
      toast.error("Please fill in all bank account fields.");
      return;
    }

    // Check if a branch is selected
    if (!globalBranchId) {
      toast.error("Please select a branch from the global branch selector.");
      return;
    }

    // Role check - Allow 'Admin' or 'Superadmin'
    const userRoleUpper = user?.role?.toUpperCase();
    if (userRoleUpper !== "ADMIN" && userRoleUpper !== "SUPERADMIN") {
      toast.error("You do not have permission to add bank accounts.");
      return;
    }

    if (editingBankAccount) {
      // Update existing bank account
      submitUpdateBankAccount({
        id: editingBankAccount.id,
        payload: formWithBranchId,
      });
    } else {
      // Add new bank account
      submitAddBankAccount(formWithBranchId);
    }
  };

  // Function to open the dialog for editing a bank account
  const handleEditBankAccountClick = useCallback((account: BankAccount) => {
    setEditingBankAccount(account);
    // Pre-fill form with existing bank account data
    setBankAccountForm({
      account_name: account.account_name,
      account_number: account.account_number,
      bank_name: account.bank_name,
      branch_id: account.branch_id,
      currency: account.currency,
      payment_type: account.payment_type,
    });
    setShowAddBankAccountDialog(true);
  }, []);

  // Handle Bank Account dialog close
  const handleBankAccountDialogClose = useCallback(
    (open: boolean) => {
      if (!open) {
        // Reset form when closing
        setBankAccountForm({
          account_name: "",
          account_number: "",
          bank_name: "",
          branch_id: globalBranchId || 0,
          currency: "NGN",
          payment_type: "ProphetOffering",
        });
        setEditingBankAccount(null);
      }
      setShowAddBankAccountDialog(open);
    },
    [globalBranchId]
  );

  // Refetch branches/managers/bank accounts/countries when the relevant tabs become active
  useEffect(() => {
    if (activeTab === "locations" || activeTab === "profile") {
      refetchBranches();
    }
    if (activeTab === "administration" && activeAdminTab === "all-users") {
      refetchManagers();
    }
    if (activeTab === "bankAccount") {
      refetchBankAccounts();
    }
  }, [
    activeTab,
    activeAdminTab,
    refetchBranches,
    refetchManagers,
    refetchBankAccounts,
  ]);

  const handleCopyEmail = () => {
    if (user?.email) {
      navigator.clipboard.writeText(user.email);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    }
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <main className="container mx-auto px-6 pb-3 min-h-[100vh]">
      <div className="h-[100vh] bg-white rounded-2xl">
        <div className="flex h-full w-full relative">
          {/* Sidebar */}
          <div className="bg-[#FBFBFB] rounded-l-2xl w-[28%] border-r p-4 flex flex-col h-full">
            <div className="flex items-center mb-16">
              <h2 className="text-sm">SETTINGS</h2>
            </div>
            <div className="space-y-4">
              <div>
                <h3 className="text-sm text-[#9C9A9A] mb-2">PERSONAL</h3>
                <div className="space-y-1">
                  <button
                    className={cn(
                      "w-full text-left px-3 py-2 rounded-md text-sm",
                      activeTab === "profile"
                        ? "bg-[#AD33E31A] text-black"
                        : "hover:bg-gray-100"
                    )}
                    onClick={() => setActiveTab("profile")}
                  >
                    Profile
                  </button>
                  <button
                    className={cn(
                      "w-full text-left px-3 py-2 rounded-md text-sm",
                      activeTab === "password"
                        ? "bg-[#AD33E31A] text-black"
                        : "hover:bg-gray-100"
                    )}
                    onClick={() => setActiveTab("password")}
                  >
                    Password
                  </button>
                </div>
              </div>
              {user?.role?.toUpperCase() === "ADMIN" && (
                <>
                  <div>
                    <h3 className="text-sm text-[#9C9A9A] mb-2">COMPANY</h3>
                    <div className="space-y-1">
                      <button
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-md text-sm",
                          activeTab === "administration"
                            ? "bg-[#AD33E31A] text-black"
                            : "hover:bg-gray-100"
                        )}
                        onClick={() => setActiveTab("administration")}
                      >
                        Administration
                      </button>
                      <button
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-md text-sm",
                          activeTab === "locations"
                            ? "bg-[#AD33E31A] text-black"
                            : "hover:bg-gray-100"
                        )}
                        onClick={() => setActiveTab("locations")}
                      >
                        Locations
                      </button>
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm text-[#9C9A9A] mb-2">ACCOUNT</h3>
                    <div className="space-y-1">
                      <button
                        className={cn(
                          "w-full text-left px-3 py-2 rounded-md text-sm",
                          activeTab === "bankAccount"
                            ? "bg-[#AD33E31A] text-black"
                            : "hover:bg-gray-100"
                        )}
                        onClick={() => setActiveTab("bankAccount")}
                      >
                        Bank Account
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
            <div
              className="py-2 px-4 mt-3 rounded-md cursor-pointer text-red-500"
              onClick={handleLogout}
            >
              Log Out
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 ml-6 overflow-y-auto">
            <div className="absolute top-4 right-4">
              {activeTab === "password" && (
                <Button className="bg-black text-white rounded-full">
                  <span className="mr-2">Update</span>
                </Button>
              )}
              {activeTab === "company-profile" && (
                <Button className="bg-black text-white rounded-full">
                  <span className="mr-2">Update</span>
                </Button>
              )}
              {activeTab === "administration" &&
                activeAdminTab === "all-users" && (
                  <Button
                    className="bg-black text-white rounded-full"
                    onClick={() => setShowAddUserDialog(true)}
                  >
                    <span className="mr-2">Add User</span>
                  </Button>
                )}
              {activeTab === "locations" && (
                <Button
                  className="bg-black text-white rounded-full"
                  onClick={() => setShowAddLocationDialog(true)}
                >
                  <span className="mr-2">Add Location</span>
                </Button>
              )}
              {activeTab === "bankAccount" && (
                <Button
                  className="bg-black text-white rounded-full"
                  onClick={() => setShowAddBankAccountDialog(true)}
                >
                  <span className="mr-2">Add Bank Account</span>
                </Button>
              )}
            </div>
            {/* Personal Profile */}
            {activeTab === "profile" && (
              <div className="w-full p-4">
                <div className="flex flex-col max-w-md pt-12">
                  <div className="flex flex-col justify-start">
                    <Image
                      src="/placeholder.png"
                      alt="Image Placeholder"
                      width={100}
                      height={100}
                      className="w-32 -ml-8 h-32 object-cover"
                    />
                  </div>
                  <div className="my-3 flex items-center justify-between">
                    <div className="text-green-500 font-medium">
                      {user?.role}
                    </div>
                    <div className="text-gray-500 text-sm">
                      Created: 21/03/2025
                    </div>
                  </div>

                  <div className="space-y-6">
                    <div>
                      <label className="block text-gray-500 text-sm mb-2">
                        FULL NAME
                      </label>
                      <Input
                        type="text"
                        defaultValue={`${user?.firstName} ${user?.lastName}`}
                        className="bg-gray-50 border-0 rounded-full"
                        readOnly
                      />
                    </div>

                    <div>
                      <label className="block text-gray-500 text-sm mb-2">
                        EMAIL
                      </label>
                      <div className="relative">
                        <Input
                          disabled
                          type="email"
                          defaultValue={user?.email}
                          className="bg-gray-50 border-0 pr-10 rounded-full"
                          readOnly
                        />
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute right-0 top-1/2 -translate-y-1/2"
                          onClick={handleCopyEmail}
                        >
                          {copied ? (
                            <Check size={16} className="text-green-500" />
                          ) : (
                            <Copy size={16} />
                          )}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-8">
                  <label className="flex items-center text-gray-500 text-sm mb-2">
                    <Globe size={16} className="mr-2" />
                    LOCATIONS
                  </label>
                  {isLoadingBranches ? (
                    <Skeleton className="h-16 w-full rounded-lg" />
                  ) : branchesError ? (
                    <div className="text-sm text-red-500 p-3">
                      Error loading locations. Please try again later.
                    </div>
                  ) : (
                    <div className="text-sm text-gray-700 p-3">
                      {user?.role === "Admin"
                        ? // For Admin users, show all branches
                          branchesData?.branches &&
                          branchesData.branches.length > 0
                          ? branchesData.branches.map((branch, index) => (
                              <span key={branch.id}>
                                {branch.name}, {branch.city}, {branch.state},{" "}
                                {branch.country}
                                {index < branchesData.branches.length - 1
                                  ? "; "
                                  : ""}
                              </span>
                            ))
                          : "No locations available."
                        : // For Manager/Support users, show only their assigned branch
                        availableBranches.length > 0
                        ? availableBranches.map((branch, index) => (
                            <span key={branch.id}>
                              {branch.name}, {branch.city}, {branch.state},{" "}
                              {branch.country}
                              {index < availableBranches.length - 1 ? "; " : ""}
                            </span>
                          ))
                        : "No assigned location."}
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Password Change */}
            {activeTab === "password" && (
              <div className="w-full p-4">
                <div className="flex flex-col max-w-md pt-12">
                  <div className="flex flex-col justify-start">
                    <Image
                      src="/placeholder.png"
                      alt="Image Placeholder"
                      width={100}
                      height={100}
                      className="w-32 -ml-8 h-32 object-cover"
                    />
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-500 text-sm mb-2">
                      OLD PASSWORD
                    </label>
                    <Input
                      type="password"
                      defaultValue="•••••••"
                      className="bg-[#f9fafb] border-0 rounded-full"
                    />
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-500 text-sm mb-2">
                      NEW PASSWORD
                    </label>
                    <Input
                      type="password"
                      className="bg-[#f9fafb] border-0 rounded-full"
                    />
                  </div>

                  <div className="mb-6">
                    <label className="block text-gray-500 text-sm mb-2">
                      CONFIRM PASSWORD
                    </label>
                    <Input
                      type="password"
                      className="bg-[#f9fafb] border-0 rounded-full"
                    />
                  </div>
                </div>
              </div>
            )}

            {/* Administration */}
            {activeTab === "administration" && (
              <div className="w-full p-4">
                <div className="flex flex-col pt-12">
                  <Tabs
                    defaultValue="all-users"
                    onValueChange={setActiveAdminTab}
                  >
                    <TabsList className="mb-6 bg-[#f9fafb] p-1 rounded-full">
                      <TabsTrigger
                        value="all-users"
                        className="rounded-full px-6 data-[state=active]:border-[#ad33e3] data-[state=active]:text-black"
                      >
                        ALL USERS{" "}
                        <span className="ml-2 bg-gray-200 text-black px-2 py-0.5 rounded-full text-xs">
                          {managersData?.managers?.length}
                        </span>
                      </TabsTrigger>
                    </TabsList>

                    <TabsContent value="all-users">
                      {isLoadingManagers ? (
                        // Loading Skeletons for Managers
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {[...Array(4)].map((_, i) => (
                            <Skeleton
                              key={i}
                              className="h-28 w-full rounded-lg"
                            />
                          ))}
                        </div>
                      ) : managersError ? (
                        // Error Message for Managers
                        <div className="text-center text-red-500 py-4">
                          Error loading users: {managersError.message}
                        </div>
                      ) : managersData?.managers &&
                        managersData.managers.length > 0 ? (
                        // Display Managers Grid
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          {managersData.managers.map((manager) => (
                            <div
                              key={manager.id}
                              className="bg-white rounded-lg p-4 shadow-sm border border-gray-100"
                            >
                              <div className="flex justify-between items-start">
                                <div className="flex items-center">
                                  {/* Placeholder Image/Initial - Replace with actual image if available */}
                                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-gray-500 font-medium mr-3">
                                    {`${
                                      manager.first_name?.[0]?.toUpperCase() ||
                                      ""
                                    }${
                                      manager.last_name?.[0]?.toUpperCase() ||
                                      ""
                                    }`}
                                  </div>
                                  <div>
                                    <div className="font-medium">
                                      {manager.first_name} {manager.last_name}
                                    </div>
                                    <div className="text-sm text-gray-500">
                                      {manager.email}
                                    </div>
                                    <div className="text-sm text-green-500 capitalize">
                                      {manager.role?.toLowerCase()}
                                    </div>
                                  </div>
                                </div>
                                {/* Edit/Actions Dropdown */}
                                <DropdownMenu>
                                  <DropdownMenuTrigger asChild>
                                    <Button
                                      variant="ghost"
                                      size="icon"
                                      className="text-gray-400 h-8 w-8"
                                    >
                                      <MoreVertical size={16} />
                                    </Button>
                                  </DropdownMenuTrigger>
                                  <DropdownMenuContent align="end">
                                    <DropdownMenuItem
                                      onClick={() =>
                                        handleEditManagerClick(manager)
                                      }
                                    >
                                      Edit User
                                    </DropdownMenuItem>
                                    {/* Add other actions like 'Deactivate' or 'Delete' if needed */}
                                    {/* <DropdownMenuItem className="text-red-500">Delete User</DropdownMenuItem> */}
                                  </DropdownMenuContent>
                                </DropdownMenu>
                              </div>
                              <div className="mt-3 flex items-center text-sm text-gray-500">
                                <Globe size={14} className="mr-1" />
                                {/* Display first branch name or manager's location info */}
                                <span>
                                  {manager.branches?.[0]?.name || // Use first branch name if available
                                    `${
                                      manager.address
                                        ? manager.address + ", "
                                        : ""
                                    }${
                                      manager.state ? manager.state + ", " : ""
                                    }${manager.country || ""}` || // Otherwise use manager's address/state/country
                                    "N/A"}
                                </span>
                                {/* Add created date if available in API response */}
                                {/* <span className="ml-auto text-xs">Created: {manager.createdAt || 'N/A'}</span> */}
                              </div>
                            </div>
                          ))}
                        </div>
                      ) : (
                        // Empty State for Managers
                        <div className="text-center text-gray-500 py-4">
                          No users found. Add a new user to get started.
                        </div>
                      )}
                    </TabsContent>
                  </Tabs>
                </div>
              </div>
            )}

            {/* Bank Accounts Tab */}
            {activeTab === "bankAccount" && (
              <div className="w-full p-4 overflow-y-auto">
                <div className="flex flex-col pt-12">
                  {/* Display Bank Accounts */}
                  {isLoadingBankAccounts ? (
                    // Loading Skeletons
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {[...Array(8)].map((_, i) => (
                        <Skeleton key={i} className="h-16 w-full rounded-lg" />
                      ))}
                    </div>
                  ) : bankAccountsError ? (
                    // Error Message
                    <div className="text-center text-red-500 py-4">
                      Error loading bank accounts: {bankAccountsError.message}
                    </div>
                  ) : bankAccountsData?.result &&
                    bankAccountsData.result.length > 0 ? (
                    // Bank Accounts Grid
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {bankAccountsData.result.map((account: BankAccount) => (
                        <div
                          key={account.id}
                          className="bg-white rounded-lg p-3 shadow-sm border border-gray-100 relative group cursor-pointer"
                          onClick={() => handleEditBankAccountClick(account)}
                        >
                          <div className="font-medium truncate pr-6">
                            {account.bank_name}
                          </div>
                          <Edit2 className="absolute top-2 right-2 h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="text-xs text-gray-500 truncate">
                            {account.account_name}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {account.account_number}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {account.currency} | {account.payment_type}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    // Empty State
                    <div className="text-center text-gray-500 py-4">
                      No bank accounts available for this location. Add a new
                      bank account to get started.
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Locations Tab */}
            {activeTab === "locations" && (
              <div className="w-full p-4 overflow-y-auto">
                <div className="flex flex-col pt-12">
                  {" "}
                  {/* Reduced top padding */}
                  {/* Search Bar - Keep if needed, functionality not implemented here */}
                  {/* <div className="mb-6"> ... </div> */}
                  {/* Display Branches */}
                  {isLoadingBranches ? (
                    // Loading Skeletons
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {[...Array(8)].map((_, i) => (
                        <Skeleton key={i} className="h-16 w-full rounded-lg" />
                      ))}
                    </div>
                  ) : branchesError ? (
                    // Error Message
                    <div className="text-center text-red-500 py-4">
                      Error loading locations: {branchesError.message}
                    </div>
                  ) : branchesData?.branches &&
                    branchesData.branches.length > 0 ? (
                    // Branches Grid
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {branchesData.branches.map((branch) => (
                        <div
                          key={branch.id}
                          className="bg-white rounded-lg p-3 shadow-sm border border-gray-100 relative group cursor-pointer"
                          onClick={() => handleEditClick(branch)}
                        >
                          <div className="font-medium truncate pr-6">
                            {" "}
                            {/* Add padding for icon */}
                            {branch.name}
                          </div>
                          {/* Edit Icon - Show on hover */}
                          <Edit2 className="absolute top-2 right-2 h-4 w-4 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                          <div className="text-xs text-gray-500 truncate">
                            {branch.city}, {branch.state}, {branch.country}
                          </div>
                          <div className="text-xs text-gray-500 truncate">
                            {branch.address}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    // Empty State
                    <div className="text-center text-gray-500 py-4">
                      No locations found. Add a new location to get started.
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Add User Dialog */}
        <Dialog
          open={showAddUserDialog}
          onOpenChange={handleAddUserDialogClose}
        >
          <DialogContent className="sm:max-w-2xl h-[90%] overflow-y-auto">
            <DialogHeader>
              <DialogTitle className="text-lg">NEW USER</DialogTitle>
              <DialogDescription>
                Add a new user to the system with their details and assigned
                branch.
              </DialogDescription>
            </DialogHeader>

            <form onSubmit={handleNewUserSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    FIRST NAME
                  </label>
                  <Input
                    type="text"
                    className="bg-[#f9fafb] rounded-full border-0"
                    value={newUserForm.first_name}
                    onChange={(e) =>
                      handleNewUserFormChange("first_name", e.target.value)
                    }
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    LAST NAME
                  </label>
                  <Input
                    type="text"
                    className="bg-[#f9fafb] rounded-full border-0"
                    value={newUserForm.last_name}
                    onChange={(e) =>
                      handleNewUserFormChange("last_name", e.target.value)
                    }
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    EMAIL
                  </label>
                  <Input
                    type="email"
                    className="bg-[#f9fafb] border-0 rounded-full"
                    value={newUserForm.email}
                    onChange={(e) =>
                      handleNewUserFormChange("email", e.target.value)
                    }
                    required
                  />
                </div>
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    PHONE
                  </label>
                  <Input
                    type="tel"
                    className="bg-[#f9fafb] border-0 rounded-full"
                    value={newUserForm.phone}
                    onChange={(e) =>
                      handleNewUserFormChange("phone", e.target.value)
                    }
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    PASSWORD
                  </label>
                  <div className="relative">
                    <Input
                      type={showPassword ? "text" : "password"}
                      className="bg-[#f9fafb] border-0 rounded-full pr-10"
                      value={newUserForm.password}
                      onChange={(e) =>
                        handleNewUserFormChange("password", e.target.value)
                      }
                      required
                    />
                    <button
                      type="button"
                      className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff size={16} /> : <Eye size={16} />}
                    </button>
                  </div>
                </div>
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    ROLE
                  </label>
                  <Select
                    value={newUserForm.role_name}
                    onValueChange={(value) =>
                      handleNewUserFormChange("role_name", value)
                    }
                  >
                    <SelectTrigger className="bg-[#f9fafb] items-center justify-between border-0 rounded-full">
                      <SelectValue placeholder="Select Role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Admin">Admin</SelectItem>
                      <SelectItem value="Manager">Manager</SelectItem>
                      <SelectItem value="Support">Support</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div>
                <label className="block text-gray-500 text-sm mb-2">
                  ASSIGNED BRANCH
                </label>
                <div className="bg-[#f9fafb] rounded-full p-3 text-sm text-gray-700">
                  {globalBranchId
                    ? branchesData?.branches?.find(
                        (b) => b.id === globalBranchId
                      )?.name || "Loading branch information..."
                    : "No branch selected. Please select a branch from the global branch selector."}
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  User will be assigned to the currently selected branch in the
                  global branch selector.
                </p>
              </div>

              <DialogFooter className="pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleAddUserDialogClose(false)}
                  className="rounded-full"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-black text-white rounded-full"
                  disabled={isRegisteringUser}
                >
                  {isRegisteringUser ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating...
                    </>
                  ) : (
                    "Create User"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>

        {/* Add/Edit Location Dialog */}
        <Dialog
          open={showAddLocationDialog}
          onOpenChange={handleDialogClose} // Use custom close handler
        >
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              {/* Dynamic Title */}
              <DialogTitle className="text-lg">
                {editingBranch ? "EDIT LOCATION" : "NEW LOCATION"}
              </DialogTitle>
            </DialogHeader>
            {/* Form for adding/editing location */}
            <form onSubmit={handleLocationSubmit} className="space-y-4">
              {" "}
              {/* Use combined submit handler */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="country"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    COUNTRY
                  </label>
                  {isLoadingCountries ? (
                    <Skeleton className="h-10 w-full rounded-full" />
                  ) : countriesError ? (
                    <Input
                      id="country"
                      type="text"
                      value={locationForm.country}
                      onChange={(e) =>
                        handleLocationFormChange("country", e.target.value)
                      }
                      className="bg-[#f9fafb] border-0 rounded-full"
                      required
                      placeholder="Enter country name"
                    />
                  ) : (
                    <Select
                      value={locationForm.country}
                      onValueChange={(value) =>
                        handleLocationSelectChange("country", value)
                      }
                      required
                    >
                      <SelectTrigger
                        id="country"
                        className="bg-[#f9fafb] rounded-full items-center justify-start border-0"
                      >
                        <SelectValue placeholder="Select country" />
                      </SelectTrigger>
                      <SelectContent className="max-h-[300px]">
                        {countriesData && countriesData.length > 0 ? (
                          countriesData.map((country) => (
                            <SelectItem
                              key={country.country_code}
                              value={country.country}
                            >
                              <div className="flex items-center">
                                {country.flag_png ? (
                                  <Image
                                    src={country.flag_png}
                                    alt={`${country.country} flag`}
                                    width={16}
                                    height={16}
                                    className="w-4 h-4 mr-2 rounded-sm object-cover"
                                    unoptimized
                                    onError={() => {
                                      // This will be handled by the fallback UI
                                      console.log(
                                        `Failed to load flag for ${country.country}`
                                      );
                                    }}
                                  />
                                ) : (
                                  <span className="w-4 h-4 mr-2 rounded-sm bg-gray-200 flex items-center justify-center text-xs">
                                    {country.country_code.substring(0, 2)}
                                  </span>
                                )}
                                {country.country}
                              </div>
                            </SelectItem>
                          ))
                        ) : (
                          <>
                            <SelectItem value="Nigeria">Nigeria</SelectItem>
                            <SelectItem value="United Kingdom">
                              United Kingdom
                            </SelectItem>
                            <SelectItem value="UAE">UAE</SelectItem>
                            <SelectItem value="USA">USA</SelectItem>
                          </>
                        )}
                      </SelectContent>
                    </Select>
                  )}
                </div>
                <div>
                  <div>
                    <label
                      htmlFor="state"
                      className="block text-gray-500 text-sm mb-2"
                    >
                      STATE
                    </label>
                    <Input
                      id="city"
                      type="text"
                      value={locationForm.state}
                      onChange={(e) =>
                        handleLocationFormChange("state", e.target.value)
                      }
                      className="bg-[#f9fafb] border-0 rounded-full"
                      required
                    />
                  </div>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="city"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    CITY
                  </label>
                  <Input
                    id="city"
                    type="text"
                    value={locationForm.city}
                    onChange={(e) =>
                      handleLocationFormChange("city", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="address"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    ADDRESS
                  </label>
                  <Input
                    id="address"
                    type="text"
                    value={locationForm.address}
                    onChange={(e) =>
                      handleLocationFormChange("address", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
              </div>
              <div>
                <label
                  htmlFor="name"
                  className="block text-gray-500 text-sm mb-2"
                >
                  BRANCH NAME
                </label>
                <Input
                  id="name"
                  type="text"
                  value={locationForm.name}
                  onChange={(e) =>
                    handleLocationFormChange("name", e.target.value)
                  }
                  className="bg-[#f9fafb] border-0 rounded-full"
                  required
                />
              </div>
              <div className="flex justify-end pt-4">
                <Button
                  type="submit"
                  className="bg-black text-white rounded-full"
                  disabled={
                    isAddingBranch ||
                    isUpdatingBranch || // Disable if adding or updating
                    (user?.role?.toUpperCase() !== "ADMIN" &&
                      user?.role?.toUpperCase() !== "SUPERADMIN")
                  }
                >
                  {isAddingBranch || isUpdatingBranch ? ( // Show loader if adding or updating
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : editingBranch ? ( // Change button text for edit
                    "Update"
                  ) : (
                    "Save"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Add/Edit Bank Account Dialog */}
        <Dialog
          open={showAddBankAccountDialog}
          onOpenChange={handleBankAccountDialogClose}
        >
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-lg">
                {editingBankAccount ? "EDIT BANK ACCOUNT" : "NEW BANK ACCOUNT"}
              </DialogTitle>
            </DialogHeader>
            <form onSubmit={handleBankAccountSubmit} className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="bank_name"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    BANK NAME
                  </label>
                  <Input
                    id="bank_name"
                    type="text"
                    value={bankAccountForm.bank_name}
                    onChange={(e) =>
                      handleBankAccountFormChange("bank_name", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="account_name"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    ACCOUNT NAME
                  </label>
                  <Input
                    id="account_name"
                    type="text"
                    value={bankAccountForm.account_name}
                    onChange={(e) =>
                      handleBankAccountFormChange(
                        "account_name",
                        e.target.value
                      )
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="account_number"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    ACCOUNT NUMBER
                  </label>
                  <Input
                    id="account_number"
                    type="text"
                    value={bankAccountForm.account_number}
                    onChange={(e) =>
                      handleBankAccountFormChange(
                        "account_number",
                        e.target.value
                      )
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="currency"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    CURRENCY
                  </label>
                  <Select
                    value={bankAccountForm.currency}
                    onValueChange={(value) =>
                      handleBankAccountSelectChange("currency", value)
                    }
                  >
                    <SelectTrigger
                      id="currency"
                      className="bg-[#f9fafb] border-0 rounded-full items-center justify-between"
                    >
                      <SelectValue placeholder="Select currency" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="NGN">NGN</SelectItem>
                      <SelectItem value="USD">USD</SelectItem>
                      <SelectItem value="GBP">GBP</SelectItem>
                      <SelectItem value="EUR">EUR</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="payment_type"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    PAYMENT TYPE
                  </label>
                  <Select
                    value={bankAccountForm.payment_type}
                    onValueChange={(value) =>
                      handleBankAccountSelectChange("payment_type", value)
                    }
                  >
                    <SelectTrigger
                      id="payment_type"
                      className="bg-[#f9fafb] border-0 rounded-full items-center justify-between"
                    >
                      <SelectValue placeholder="Select payment type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="ProphetOffering">
                        Prophet Offering
                      </SelectItem>
                      <SelectItem value="OfferingType">Offering</SelectItem>
                      <SelectItem value="Tithe">Tithe</SelectItem>
                      <SelectItem value="Seed">Seed</SelectItem>
                      <SelectItem value="Partnership">Partnership</SelectItem>
                      <SelectItem value="FirstFruit">First Fruit</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label
                    htmlFor="branch_id"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    BRANCH
                  </label>
                  <div className="bg-[#f9fafb] rounded-full p-3 text-sm text-gray-700">
                    {globalBranchId
                      ? branchesData?.branches?.find(
                          (b) => b.id === globalBranchId
                        )?.name || "Loading branch information..."
                      : "No branch selected. Please select a branch from the global branch selector."}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    Bank account will be assigned to the currently selected
                    branch in the global branch selector.
                  </p>
                </div>
              </div>
              <div className="flex justify-end pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleBankAccountDialogClose(false)}
                  className="rounded-full mr-2"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-black text-white rounded-full"
                  disabled={
                    isAddingBankAccount ||
                    isUpdatingBankAccount ||
                    (user?.role?.toUpperCase() !== "ADMIN" &&
                      user?.role?.toUpperCase() !== "SUPERADMIN")
                  }
                >
                  {isAddingBankAccount || isUpdatingBankAccount ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      {editingBankAccount ? "Updating..." : "Saving..."}
                    </>
                  ) : editingBankAccount ? (
                    "Update"
                  ) : (
                    "Save"
                  )}
                </Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>

        {/* Edit Manager Dialog */}
        <Dialog
          open={showEditManagerDialog}
          onOpenChange={handleEditManagerDialogClose}
        >
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle className="text-lg">EDIT USER DETAILS</DialogTitle>
              <DialogDescription>
                Update the details for {editingManager?.first_name}{" "}
                {editingManager?.last_name}.
              </DialogDescription>
            </DialogHeader>
            <form onSubmit={handleManagerSubmit} className="space-y-4 pt-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="manager-first-name"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    FIRST NAME
                  </label>
                  <Input
                    id="manager-first-name"
                    type="text"
                    value={managerForm.first_name || ""}
                    onChange={(e) =>
                      handleManagerFormChange("first_name", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
                <div>
                  <label
                    htmlFor="manager-last-name"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    LAST NAME
                  </label>
                  <Input
                    id="manager-last-name"
                    type="text"
                    value={managerForm.last_name || ""}
                    onChange={(e) =>
                      handleManagerFormChange("last_name", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                {/* <div>
                  <label
                    htmlFor="manager-email"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    EMAIL
                  </label>
                  <Input
                    id="manager-email"
                    type="email"
                    value={managerForm.email || ""}
                    onChange={(e) =>
                      handleManagerFormChange("email", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                    required
                  />
                </div> */}

                <div>
                  <label
                    htmlFor="manager-phone"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    PHONE
                  </label>
                  <Input
                    id="manager-phone"
                    type="tel"
                    value={managerForm.phone || ""}
                    onChange={(e) =>
                      handleManagerFormChange("phone", e.target.value)
                    }
                    className="bg-[#f9fafb] border-0 rounded-full"
                  />
                </div>

                {/* Role */}

                <div>
                  <label
                    htmlFor="manager-role"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    ROLE
                  </label>
                  <Select
                    value={managerForm.role_name || ""}
                    onValueChange={(value) =>
                      handleManagerSelectChange("role_name", value)
                    }
                  >
                    <SelectTrigger
                      id="manager-role"
                      className="bg-[#f9fafb] items-center justify-start border-0 rounded-full"
                    >
                      <SelectValue placeholder="Select role" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Admin">Admin</SelectItem>
                      <SelectItem value="Manager">Manager</SelectItem>
                      <SelectItem value="Support">Support</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* <div className="grid grid-cols-2 gap-4">
                <div>
                  <label
                    htmlFor="manager-country"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    COUNTRY
                  </label>
                  <Select
                    value={managerForm.country || ""}
                    onValueChange={(value) =>
                      handleManagerSelectChange("country", value)
                    }
                  >
                    <SelectTrigger
                      id="manager-country"
                      className="bg-[#f9fafb] rounded-full items-center justify-between border-0"
                    >
                      <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>

                      <SelectItem value="Nigeria">Nigeria</SelectItem>
                      <SelectItem value="United Kingdom">
                        United Kingdom
                      </SelectItem>
                      <SelectItem value="UAE">UAE</SelectItem>
                      <SelectItem value="USA">USA</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <label
                    htmlFor="manager-state"
                    className="block text-gray-500 text-sm mb-2"
                  >
                    STATE
                  </label>
                  <Select
                    value={managerForm.state || ""}
                    onValueChange={(value) =>
                      handleManagerSelectChange("state", value)
                    }
                  >
                    <SelectTrigger
                      id="manager-state"
                      className="bg-[#f9fafb] border-0 rounded-full items-center justify-between"
                    >
                      <SelectValue placeholder="Select state" />
                    </SelectTrigger>
                    <SelectContent>

                      <SelectItem value="Abuja">Abuja</SelectItem>
                      <SelectItem value="Lagos">Lagos</SelectItem>
                      <SelectItem value="Rivers">Rivers</SelectItem>
                      <SelectItem value="Manchester">Manchester</SelectItem>
                      <SelectItem value="Birmingham">Birmingham</SelectItem>
                      <SelectItem value="Dubai">Dubai</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div> */}

              <div className="grid grid-cols-1 gap-4">
                <div>
                  <label className="block text-gray-500 text-sm mb-2">
                    ASSIGNED BRANCH
                  </label>
                  <div className="bg-[#f9fafb] rounded-full p-3 text-sm text-gray-700">
                    {globalBranchId
                      ? branchesData?.branches?.find(
                          (b) => b.id === globalBranchId
                        )?.name || "Loading branch information..."
                      : "No branch selected. Please select a branch from the global branch selector."}
                  </div>
                  <p className="text-xs text-gray-500 mt-1">
                    User will be assigned to the currently selected branch in
                    the global branch selector.
                  </p>
                </div>
              </div>

              <DialogFooter className="pt-4">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => handleEditManagerDialogClose(false)}
                  className="rounded-full"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="bg-black text-white rounded-full"
                  disabled={
                    isUpdatingManager ||
                    (user?.role?.toUpperCase() !== "ADMIN" &&
                      user?.role?.toUpperCase() !== "SUPERADMIN")
                  }
                >
                  {isUpdatingManager ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Update User"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </main>
  );
}
