"use server";

import { cookies } from "next/headers";

export interface OfflinePaymentPayload {
  amount: number;
  branch_id: number;
  created_by: string;
  currency: string;
  member_email: string;
  payment_date: string;
  payment_reference: string;
  payment_type: string;
}

export interface OfflinePaymentResponse {
  amount_paid: number;
  branch_id: number;
  currency: string;
  email: string;
  first_name: string;
  id: string;
  is_credit_to_wallet: boolean;
  last_name: string;
  member_id: string;
  payment_mode: string;
  phone: string;
  provider: string;
  response_code: string;
  status: number;
  transaction_amount: number;
  transaction_date: string;
  transaction_id: string;
  transaction_reference: string;
  transaction_status_string: string;
  transaction_type: number;
  transaction_type_string: string;
  wallet_id: string;
}

const API_BASE_URL = process.env.BASE_URL;

export async function makeOfflinePayment(
  payload: OfflinePaymentPayload
): Promise<OfflinePaymentResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/offline-payment`;

  try {
    const response = await fetch(API_URL, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to process offline payment.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: OfflinePaymentResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error making offline payment:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to process offline payment. Please try again later.");
  }
}
