// components/transactions/FilterPopover.tsx
"use client";

import type React from "react";
import { Calendar, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Checkbox } from "@/components/ui/checkbox";
import { Calendar as CalendarComponent } from "@/components/ui/calendar";
import { cn } from "@/lib/utils";
import CurrencyFilter from "./CurrencyFilter";

type DateRangeType = { from: Date | null; to: Date | null };
type PeriodValue = "day" | "week" | "month" | "year";

interface FilterPopoverProps {
  activeFilterCount: number;
  clearAllFilters: () => void;
  dateRange: DateRangeType | null;
  calendarOpen: boolean;
  setCalendarOpen: (open: boolean) => void;
  handleDateRangeChange: (
    range: { from?: Date | undefined; to?: Date | undefined } | undefined
  ) => void;
  formatDateRange: () => string;
  typeFilters: string[];
  handleTypeFilterChange: (type: string) => void;
  statusFilters: string[];
  handleStatusFilterChange: (status: string) => void;
  localPriceRange: [number, number];
  handleLocalPriceRangeChange: (value: number[]) => void;
  handlePriceRangeCommit: (value: number[]) => void;
  period: PeriodValue | null;
  setPeriod: (value: PeriodValue | null) => void;
  periodValues: readonly PeriodValue[];
  selectedCurrency: string | null;
  onCurrencyChange: (currency: string | null) => void;
}

export default function FilterPopover({
  activeFilterCount,
  clearAllFilters,
  dateRange,
  calendarOpen,
  setCalendarOpen,
  handleDateRangeChange,
  formatDateRange,
  typeFilters,
  handleTypeFilterChange,
  statusFilters,
  handleStatusFilterChange,
  localPriceRange,
  handleLocalPriceRangeChange,
  handlePriceRangeCommit,
  period,
  setPeriod,
  periodValues,
  selectedCurrency,
  onCurrencyChange,
}: FilterPopoverProps) {
  return (
    <PopoverContent className="w-[350px] p-4" align="end">
      <div className="space-y-4 flex flex-col max-h-[80vh] overflow-y-auto">
        <div className="flex items-center justify-between">
          <h3 className="font-medium">Filters</h3>
          {activeFilterCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              className="h-8 text-[#866cef]"
              onClick={clearAllFilters}
            >
              Clear all
            </Button>
          )}
        </div>

        {/* Currency Filter (API) */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Currency</h4>
          <CurrencyFilter
            selectedCurrency={selectedCurrency}
            onCurrencyChange={onCurrencyChange}
          />
        </div>

        {/* Period Filter (API) */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Period</h4>
          <div className="flex flex-wrap gap-2">
            {periodValues.map((p) => (
              <Button
                key={p}
                variant="outline"
                size="sm"
                onClick={() => setPeriod(p === period ? null : p)}
                className={cn(
                  "capitalize h-8 px-3 text-xs",
                  period === p
                    ? "bg-[#866cef] text-white hover:bg-[#7058d3]"
                    : "hover:bg-muted/50"
                )}
              >
                {p}
              </Button>
            ))}
          </div>
        </div>

        {/* Date Range Filter (API) */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Date Range</h4>
          <Popover open={calendarOpen} onOpenChange={setCalendarOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className="w-full justify-start text-left font-normal border-dashed"
              >
                <Calendar className="mr-2 h-4 w-4" />
                {formatDateRange()}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <CalendarComponent
                initialFocus
                mode="range"
                defaultMonth={dateRange?.from || new Date()}
                selected={{
                  from: dateRange?.from ?? undefined,
                  to: dateRange?.to ?? undefined,
                }}
                onSelect={handleDateRangeChange}
                numberOfMonths={2}
              />
            </PopoverContent>
          </Popover>

          {dateRange && (dateRange.from || dateRange.to) && (
            <Button
              variant="ghost"
              size="sm"
              className="h-6 p-0 text-xs text-[#866cef]"
              onClick={() => handleDateRangeChange(undefined)}
            >
              <X className="h-3 w-3 mr-1" />
              Clear dates
            </Button>
          )}
        </div>

        {/* Transaction Type Filter (Client-side) */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Transaction Type</h4>
          <div className="grid grid-cols-2 gap-2">
            {[
              { id: "offering", label: "Offering" },
              { id: "tithe", label: "Tithe" },
              { id: "seed", label: "Seed" },
              { id: "partnership", label: "Partnership" },
              { id: "first-fruit", label: "First Fruit" },
              { id: "prophet-offering", label: "Prophet Offering" },
            ].map((type) => (
              <div key={type.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`type-${type.id}`}
                  checked={typeFilters?.includes(type.label)}
                  onCheckedChange={() => handleTypeFilterChange(type.label)}
                  className="data-[state=checked]:bg-[#866cef] data-[state=checked]:border-[#866cef]"
                />
                <label
                  htmlFor={`type-${type.id}`}
                  className="text-sm cursor-pointer"
                >
                  {type.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Status Filter */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Transaction Status</h4>
          <div className="grid grid-cols-2 gap-2">
            {[
              { id: "success", label: "SUCCESS" },
              { id: "pending", label: "PENDING" },
              { id: "failed", label: "FAILED" },
              { id: "declined", label: "DECLINED" },
              { id: "cancelled", label: "CANCELLED" },
              { id: "processing", label: "PROCESSING" },
            ].map((status) => (
              <div key={status.id} className="flex items-center space-x-2">
                <Checkbox
                  id={`status-${status.id}`}
                  checked={statusFilters?.includes(status.label)}
                  onCheckedChange={() => handleStatusFilterChange(status.label)}
                  className="data-[state=checked]:bg-[#866cef] data-[state=checked]:border-[#866cef]"
                />
                <label
                  htmlFor={`status-${status.id}`}
                  className="text-sm cursor-pointer"
                >
                  {status.label}
                </label>
              </div>
            ))}
          </div>
        </div>

        {/* Price Range Filter (API) */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Price Range (API)</h4>
          <div className="px-2">
            <Slider
              value={localPriceRange}
              min={0}
              max={10000000}
              step={10000}
              onValueChange={handleLocalPriceRangeChange}
              onValueCommit={handlePriceRangeCommit}
              className="mb-6"
            />
            <div className="flex justify-between text-xs text-gray-500">
              <span>₦{(localPriceRange[0] / 1000).toFixed(0)}k</span>
              <span>₦{(localPriceRange[1] / 1000000).toFixed(1)}M</span>
            </div>
          </div>
        </div>
      </div>
    </PopoverContent>
  );
}
