"use server";

import { cookies } from "next/headers";

export interface VerifyPaymentResponse {
  isCompleted: boolean;
  isCreditToWallet: boolean;
  status: string;
  transaction_reference: string;
}

const API_BASE_URL = process.env.BASE_URL;

export async function verifyPayment(
  transactionId: string,
  providerId: string
): Promise<VerifyPaymentResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/payments/verify?transactionId=${encodeURIComponent(transactionId)}&providerId=${encodeURIComponent(providerId)}`;

  try {
    const response = await fetch(API_URL, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to verify payment.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: VerifyPaymentResponse = await response.json();
    return data;
  } catch (error) {
    console.error("Error verifying payment:", error);
    throw error instanceof Error
      ? error
      : new Error("Failed to verify payment. Please try again later.");
  }
}
