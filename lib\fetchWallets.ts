// lib/fetchWallets.ts
"use server";

import { cookies } from "next/headers";
import type { Wallet, ApiWallet, WalletApiResponse } from "@/types/wallet";

const API_BASE_URL = process.env.BASE_URL;

// --- Fetch Wallets for a Specific User ---
export async function fetchUserWallets(
  userId: string
): Promise<{ wallets: Wallet[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }
  if (!userId) {
    throw new Error("User ID is required to fetch wallets.");
  }

  const API_URL = `${API_BASE_URL}/admin/members/${userId}/wallets`;
  console.log(`Fetching wallets for user ${userId} from:`, API_URL);

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : `Failed to fetch wallets for user ${userId}.`;
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: WalletApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error("Unexpected API response structure:", data);
      throw new Error("Failed to fetch wallets or invalid data format.");
    }

    // Map ApiWallet to Wallet, converting balance string to number
    const wallets: Wallet[] = data.result.map(
      (apiWallet: ApiWallet): Wallet => {
        const balanceNumber = parseFloat(apiWallet.balance);
        return {
          id: apiWallet.wallet_id,
          currency: apiWallet.currency,
          balance: isNaN(balanceNumber) ? 0 : balanceNumber,
        };
      }
    );

    return { wallets };
  } catch (error) {
    console.error(
      `Error fetching or processing wallets for user ${userId}:`,
      error
    );
    throw error;
  }
}
