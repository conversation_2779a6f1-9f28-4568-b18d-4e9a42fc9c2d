"use client";

import React from "react";
import ActiveFilters from "./ActiveFilters";

type DateRangeType = { from: Date | null; to: Date | null };
type PeriodValue = "day" | "week" | "month" | "year";

interface ActiveFiltersWrapperProps {
  activeFilterCount: number;
  dateRange: DateRangeType | null;
  formatDateRange: () => string;
  handleDateRangeChange: (
    range: { from?: Date | undefined; to?: Date | undefined } | undefined
  ) => void;
  priceMin: number | null;
  priceMax: number | null;
  setPriceMin: (value: number | null) => void;
  setPriceMax: (value: number | null) => void;
  setLocalPriceRange: (value: [number, number]) => void;
  typeFilters: string[];
  setTypeFilters: React.Dispatch<React.SetStateAction<string[]>>;
  period: PeriodValue | null;
  setPeriod: (value: PeriodValue | null) => void;
  selectedCurrency: string | null;
  onCurrencyChange: (currency: string | null) => void;
}

export default function ActiveFiltersWrapper(props: ActiveFiltersWrapperProps) {
  const adaptedSetTypeFilters: React.Dispatch<
    React.SetStateAction<string[]>
  > = (value) => {
    if (typeof value === "function") {
      props.setTypeFilters(value);
    } else {
      props.setTypeFilters(value);
    }
  };

  return (
    <ActiveFilters
      activeFilterCount={props.activeFilterCount}
      dateRange={props.dateRange}
      formatDateRange={props.formatDateRange}
      handleDateRangeChange={props.handleDateRangeChange}
      priceMin={props.priceMin}
      priceMax={props.priceMax}
      setPriceMin={props.setPriceMin}
      setPriceMax={props.setPriceMax}
      setLocalPriceRange={props.setLocalPriceRange}
      typeFilters={props.typeFilters}
      setTypeFilters={adaptedSetTypeFilters}
      period={props.period}
      setPeriod={props.setPeriod}
      selectedCurrency={props.selectedCurrency}
      onCurrencyChange={props.onCurrencyChange}
    />
  );
}
