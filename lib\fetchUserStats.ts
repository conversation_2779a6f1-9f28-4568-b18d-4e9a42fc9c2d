// lib/fetchUserStats.ts
"use server";

import { cookies } from "next/headers";
import type { UserStatsData, UserStatsApiResponse } from "@/types/stats";

const API_BASE_URL = process.env.BASE_URL;

// --- Fetch User Statistics ---
export async function fetchUserStats(): Promise<UserStatsData> {
  // Fallback data to use when the API endpoint is not available
  const fallbackData: UserStatsData = {
    all_users: 0,
    data: [],
  };
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!API_BASE_URL) {
    throw new Error("BASE_URL environment variable is not set.");
  }
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const API_URL = `${API_BASE_URL}/admin/members/statistics`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      // For 404 errors, return the fallback data instead of throwing an error
      if (response.status === 404) {
        console.warn(
          `API Endpoint not found (404) - ${API_URL} - Using fallback data`
        );
        return fallbackData;
      }

      // For other errors, try to parse JSON but handle gracefully if it fails
      let errorMessage = "Failed to fetch user statistics.";
      try {
        // Check if there's content to parse
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const errorData = await response.json();
          if (
            typeof errorData === "object" &&
            errorData !== null &&
            "message" in errorData &&
            typeof errorData.message === "string"
          ) {
            errorMessage = errorData.message;
          }
          console.error(`API Error Response (${response.status}):`, errorData);
        } else {
          // If not JSON, try to get text content
          const textContent = await response.text();
          console.error(
            `API Error Response (${response.status}):`,
            textContent ? textContent : "No response body"
          );
        }
      } catch (e) {
        console.error("Failed to parse error response:", e);
      }

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: UserStatsApiResponse = await response.json();

    if (data.message !== "success" || !data.result) {
      console.error("Unexpected API response structure for stats:", data);
      throw new Error("Failed to fetch user stats or invalid data format.");
    }

    return data.result;
  } catch (error) {
    console.error("Error fetching or processing user stats:", error);
    // Return fallback data instead of throwing an error
    console.warn("Using fallback data due to error");
    return fallbackData;
  }
}
