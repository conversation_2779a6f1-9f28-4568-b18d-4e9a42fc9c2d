/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useEffect, useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query"; // Import React Query hooks
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Pencil, Plus, Trash2 } from "lucide-react";
import { toast } from "sonner";
import DeclarationModal from "@/components/declarations/declaration-modal";
import DeclarationCardSkeleton from "@/components/declarations/DeclarationCardSkeleton";
import DeleteConfirmDialog from "@/components/declarations/DeleteConfirmDialog";
import {
  fetchDeclarations,
  createDeclaration,
  updateDeclaration,
  deleteDeclaration,
} from "@/lib/fetchDeclarations";
import type {
  Declaration,
  NewDeclarationPayload,
  UpdateDeclarationPayload,
} from "@/types/declaration";

export default function DeclarationPage() {
  const queryClient = useQueryClient();
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentDeclaration, setCurrentDeclaration] =
    useState<Declaration | null>(null);
  const [declarationToDelete, setDeclarationToDelete] = useState<number | null>(
    null
  );
  const [fetchErrorMsg, setFetchErrorMsg] = useState<string | null>(null);
  const {
    data: queryResult,
    isLoading,
    isError,
    error: queryError,
  } = useQuery<{ declarations: Declaration[] }>({
    queryKey: ["declarations"],
    queryFn: () => fetchDeclarations(),
    placeholderData: (previousData) => previousData,
  });

  const declarations = queryResult?.declarations ?? [];

  useEffect(() => {
    if (isError && queryError && !fetchErrorMsg) {
      const message =
        (queryError as Error).message || "Failed to load declarations.";
      console.error("Failed to load declarations:", queryError);
      toast.error(message);
      setFetchErrorMsg(message);
    } else if (!isError && fetchErrorMsg) {
      setFetchErrorMsg(null);
    }
  }, [isError, queryError, fetchErrorMsg]);

  const { mutate: createMutate, isPending: isSaving } = useMutation({
    mutationFn: createDeclaration,
    onSuccess: () => {
      toast.success("Declaration created successfully!");
      setIsModalOpen(false);
      queryClient.invalidateQueries({ queryKey: ["declarations"] });
    },
    onError: (error: any) => {
      console.error("Failed to create declaration:", error);
      toast.error(error.message || "Failed to create declaration.");
    },
  });

  const { mutate: updateMutate, isPending: isUpdating } = useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: number;
      payload: UpdateDeclarationPayload;
    }) => updateDeclaration(id, payload),
    onSuccess: (data) => {
      toast.success(data.message || "Declaration updated successfully!");
      setIsModalOpen(false);
      queryClient.invalidateQueries({ queryKey: ["declarations"] });
    },
    onError: (error: any) => {
      console.error("Failed to update declaration:", error);
      toast.error(error.message || "Failed to update declaration.");
    },
  });

  const { mutate: deleteMutate, isPending: isDeleting } = useMutation({
    mutationFn: (id: number) => deleteDeclaration(id),
    onSuccess: (data) => {
      toast.success(data.message || "Declaration deleted successfully!");
      setIsDeleteDialogOpen(false);
      setDeclarationToDelete(null);
      queryClient.invalidateQueries({ queryKey: ["declarations"] });
    },
    onError: (error: any) => {
      console.error("Failed to delete declaration:", error);
      toast.error(error.message || "Failed to delete declaration.");
      setIsDeleteDialogOpen(false);
    },
  });

  const handleCreateNew = () => {
    if (isSaving) return;
    setCurrentDeclaration(null);
    setIsModalOpen(true);
  };

  const handleEdit = (declaration: Declaration) => {
    if (isSaving || isUpdating) return;
    setCurrentDeclaration(declaration);
    setIsModalOpen(true);
  };

  const handleDelete = (id: number) => {
    if (isDeleting || isSaving || isUpdating) return;
    setDeclarationToDelete(id);
    setIsDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (declarationToDelete !== null) {
      deleteMutate(declarationToDelete);
    }
  };

  // Handle saving (create or update)
  const handleSave = (modalData: any) => {
    if (!modalData.text || modalData.text.trim() === "") {
      toast.error("Declaration text cannot be empty.");
      return;
    }

    if (currentDeclaration) {
      const updatePayload: UpdateDeclarationPayload = {};
      let hasChanges = false;

      if (modalData.text.trim() !== currentDeclaration.text) {
        updatePayload.text = modalData.text.trim();
        hasChanges = true;
      }
      if (modalData.background_color !== currentDeclaration.background_color) {
        updatePayload.background_color = modalData.background_color;
        hasChanges = true;
      }
      if (modalData.text_color !== currentDeclaration.text_color) {
        updatePayload.text_color = modalData.text_color;
        hasChanges = true;
      }

      if (!hasChanges) {
        toast.info("No changes detected.");
        setIsModalOpen(false);
        return;
      }
      updateMutate({ id: currentDeclaration.id, payload: updatePayload });
    } else {
      const createPayload: NewDeclarationPayload = {
        background_color: modalData.background_color,
        declarations: [modalData.text.trim()],
        text_color: modalData.text_color,
      };
      createMutate(createPayload);
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString("en-US", {
        day: "numeric",
        month: "long",
        year: "numeric",
      });
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
    } catch (e) {
      return "Invalid Date";
    }
  };

  return (
    <main className="container mx-auto px-6 py-6">
      <Card className="h-[740px] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center mb-6">
            <h2 className="text-xl font-bold">DECLARATIONS</h2>
            <span className="ml-2 text-sm bg-gray-100 px-2 py-1 rounded-md">
              {/* Show count based on fetched data, handle loading state */}
              {isLoading ? "..." : declarations.length}
            </span>
          </div>

          {/* Content Grid - Show Skeletons or Declarations */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 overflow-y-auto">
            {/* Create New Card - Always visible unless loading initial data */}
            {!isLoading && (
              <Card
                className="relative flex flex-col items-center justify-center p-6 h-[300px] cursor-pointer bg-[#F8F8F8] hover:bg-gray-100 transition-colors"
                onClick={handleCreateNew}
                aria-disabled={isSaving}
                style={{ opacity: isSaving ? 0.7 : 1 }}
              >
                <div className="absolute top-2 left-2 w-9 h-9 rounded-full bg-white flex items-center justify-center mb-4">
                  <Plus className="h-6 w-6" />
                </div>
                <div className="absolute bottom-4 left-4 flex flex-col items-start justify-center">
                  {" "}
                  {/* Adjusted alignment */}
                  <h3 className="text-lg text-[#C1C1C2]">CREATE NEW</h3>
                  <p className="text-sm mt-1 text-[#C1C1C2]">
                    {" "}
                    {/* Adjusted margin */}
                    {new Date().toLocaleDateString("en-US", {
                      day: "numeric",
                      month: "long",
                      year: "numeric",
                    })}
                  </p>
                </div>
              </Card>
            )}

            {/* Skeletons or Declaration Cards */}
            {isLoading
              ? // Show skeletons while loading initial data
                Array.from({ length: 3 }).map((_, index) => (
                  <DeclarationCardSkeleton key={`skeleton-${index}`} />
                ))
              : // Show declarations once loaded
                declarations.map((declaration) => (
                  <Card
                    key={declaration.id}
                    className="relative h-[300px] overflow-hidden"
                    style={{ backgroundColor: declaration.background_color }}
                  >
                    <Button
                      variant="ghost"
                      size="icon"
                      className="absolute top-2 left-2 bg-white/20 hover:bg-white/30 rounded-full"
                      onClick={() => handleEdit(declaration)}
                      disabled={isSaving || isUpdating}
                    >
                      <Pencil className="h-4 w-4 text-white" />
                    </Button>

                    <div className="p-6 flex flex-col h-full">
                      <div
                        style={{ color: declaration.text_color }}
                        className="flex-1 flex items-center overflow-hidden"
                      >
                        {/* Improved text handling with overflow protection */}
                        <h3 className="text-lg break-words whitespace-pre-wrap overflow-hidden text-ellipsis max-h-[200px]">
                          {declaration.text}
                        </h3>
                      </div>

                      <div className="mt-auto">
                        <div className="flex justify-between items-center">
                          <p className="text-sm text-white/80">
                            {formatDate(declaration.created_at)}
                          </p>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="bg-white/20 hover:bg-white/30 rounded-full"
                            onClick={() => handleDelete(declaration.id)}
                            disabled={isSaving || isUpdating || isDeleting}
                          >
                            <Trash2 className="h-4 w-4 text-white" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </Card>
                ))}
            {/* Handle case where there's an error after loading */}
            {isError && !isLoading && (
              <Card className="sm:col-span-2 lg:col-span-4 flex items-center justify-center h-[270px] border-destructive bg-destructive/10">
                <p className="text-destructive text-center">
                  Error loading declarations. Please try again later. <br />(
                  {fetchErrorMsg})
                </p>
              </Card>
            )}
            {/* Handle case where data is loaded but empty */}
            {!isLoading && !isError && declarations.length === 0 && (
              <Card className="sm:col-span-2 lg:col-span-4 flex items-center justify-center h-[270px] border-dashed">
                <p className="text-muted-foreground text-center">
                  No declarations found. <br />
                  Click &quot;Create New&quot; to add one.
                </p>
              </Card>
            )}
          </div>
        </div>
      </Card>

      {/* Declaration Modal */}
      <DeclarationModal
        isOpen={isModalOpen}
        onClose={() => !(isSaving || isUpdating) && setIsModalOpen(false)}
        declaration={currentDeclaration}
        declarations={declarations}
        onSave={handleSave}
        isSaving={isSaving || isUpdating}
      />

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => !isDeleting && setIsDeleteDialogOpen(false)}
        onConfirm={confirmDelete}
        isDeleting={isDeleting}
      />
    </main>
  );
}
