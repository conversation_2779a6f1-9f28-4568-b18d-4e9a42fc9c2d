// components/transactions/ActiveFilters.tsx
"use client";

import type React from "react";
import { X } from "lucide-react";
import { Badge } from "@/components/ui/badge";

type DateRangeType = { from: Date | null; to: Date | null };
type PeriodValue = "day" | "week" | "month" | "year";

interface ActiveFiltersProps {
  activeFilterCount: number;
  dateRange: DateRangeType | null;
  formatDateRange: () => string;
  handleDateRangeChange: (
    range: { from?: Date | undefined; to?: Date | undefined } | undefined
  ) => void;
  priceMin: number | null;
  priceMax: number | null;
  setPriceMin: (value: number | null) => void;
  setPriceMax: (value: number | null) => void;
  setLocalPriceRange: (value: [number, number]) => void;
  typeFilters: string[];
  setTypeFilters: React.Dispatch<React.SetStateAction<string[]>>;
  period: PeriodValue | null;
  setPeriod: (value: PeriodValue | null) => void;
  selectedCurrency: string | null;
  onCurrencyChange: (currency: string | null) => void;
}

export default function ActiveFilters({
  activeFilterCount,
  dateRange,
  formatDateRange,
  handleDateRangeChange,
  priceMin,
  priceMax,
  setPriceMin,
  setPriceMax,
  setLocalPriceRange,
  typeFilters,
  setTypeFilters,
  period,
  setPeriod,
  selectedCurrency,
  onCurrencyChange,
}: ActiveFiltersProps) {
  if (activeFilterCount === 0) {
    return null;
  }

  return (
    <div className="flex flex-wrap gap-2 mb-4 items-center">
      {/* Currency Filter Badge */}
      {selectedCurrency && (
        <Badge
          variant="outline"
          className="rounded-full px-3 py-1 bg-[#F8F8F8]"
        >
          <span className="mr-1">
            Currency:{" "}
            {selectedCurrency === "NGN"
              ? "₦"
              : selectedCurrency === "GBP"
              ? "£"
              : selectedCurrency === "EUR"
              ? "€"
              : selectedCurrency === "USD"
              ? "$"
              : selectedCurrency}
          </span>
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => onCurrencyChange(null)}
          />
        </Badge>
      )}

      {/* Period Filter Badge */}
      {period && (
        <Badge
          variant="outline"
          className="rounded-full px-3 py-1 bg-[#F8F8F8] capitalize"
        >
          <span className="mr-1">Period: {period}</span>
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => setPeriod(null)}
          />
        </Badge>
      )}

      {/* Date Filter Badge */}
      {dateRange && (dateRange.from || dateRange.to) && (
        <Badge
          variant="outline"
          className="rounded-full px-3 py-1 bg-[#F8F8F8]"
        >
          <span className="mr-1">Date: {formatDateRange()}</span>
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => handleDateRangeChange(undefined)}
          />
        </Badge>
      )}

      {/* Price Filter Badge */}
      {(priceMin !== null || priceMax !== null) && (
        <Badge
          variant="outline"
          className="rounded-full px-3 py-1 bg-[#F8F8F8]"
        >
          <span className="mr-1">
            Price:{" "}
            {priceMin !== null ? `₦${(priceMin / 1000).toFixed(0)}k` : "Min"} -{" "}
            {priceMax !== null ? `₦${(priceMax / 1000000).toFixed(1)}M` : "Max"}
          </span>
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => {
              setPriceMin(null);
              setPriceMax(null);
              setLocalPriceRange([0, 10000000]);
            }}
          />
        </Badge>
      )}

      {/* Type Filter Badge */}
      {typeFilters && typeFilters.length > 0 && (
        <Badge
          variant="outline"
          className="rounded-full px-3 py-1 bg-[#F8F8F8]"
        >
          <span className="mr-1">
            Type:{" "}
            {typeFilters.length === 1
              ? typeFilters[0]
              : `${typeFilters.length} selected`}
          </span>
          <X
            className="h-3 w-3 cursor-pointer"
            onClick={() => setTypeFilters([])}
          />
        </Badge>
      )}
    </div>
  );
}
