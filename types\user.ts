// src/types/user.ts

// Structure matching the API response 'result' items
export interface ApiUser {
  id: string;
  first_name: string | null;
  last_name: string | null;
  email: string;
  phone: string | null;
  address: string | null;
  occupation: string | null;
  state: string | null;
  country: string | null;
  dob: string | null;
  branch_id: number | null;
  branch: string | null;
  branch_state: string | null;
  branch_country: string | null;
  created_at: string;
  wallets: Wallet[];
}

// --- NEW TYPE FOR WALLETS ---
export interface Wallet {
  wallet_id: string;
  member_id: string;
  currency: string;
  balance: string;
}

// Structure matching the API's top-level response for fetching ALL users
export interface UserApiResponse {
  message: string;
  result: ApiUser[];
}

// Structure matching the single user API response
export interface SingleUserApiResponse {
  message: string;
  result: ApiUser;
  status: number;
}

// Structure expected by the UI component (adjust based on components/users.tsx needs)
export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: {
    city: string;
    state: string;
    address: string;
    country: string;
    branch: string;
  };
  registeredOn: string;
  registrationTimestamp: number;
  wallets: Wallet[];
}

// Parameters for the fetchUsers function
export interface FetchUsersParams {
  start?: string;
  end?: string;
  location_id?: number;
  export?: boolean;
}

// --- NEW TYPES FOR GLOBAL DISTRIBUTION ---

export interface GlobalDistributionDataPoint {
  id: string;
  country: string;
  users: number;
  percentage: number;
  lat: number;
  lng: number;
}

// Structure for the API response of the global distribution endpoint
export interface GlobalDistributionApiResponse {
  message: string;
  totalUsers: number;
  activeUserPercentage: number;
  data: GlobalDistributionDataPoint[];
}

// (MapDataPoint definition can remain the same or be updated if needed)
export type MapDataPoint = {
  id: string | number;
  lat: number;
  lng: number;
  label: string;
  value: number;
};

// Structure for the list items displayed next to the map
export interface CountryListItem {
  id: string;
  name: string;
  flag?: string;
  percentage: number;
}
