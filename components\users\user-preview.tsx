"use client";

import Link from "next/link";
import { Download, Search, BarChart3, ExternalLink } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { useQuery, useMutation } from "@tanstack/react-query";
import { useState, useMemo, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import type { User } from "@/types/user";
import type { Wallet } from "@/types/wallet";
import type { UserTransaction } from "@/lib/fetchUserTransactions";
import type { TotalGivingAnalyticsItem } from "@/types/giving";
import { fetchUserWallets } from "@/lib/fetchWallets";
import { fetchTotalGivingAnalytics } from "@/lib/fetchTotalGiving";
import { closeUserAccount } from "@/lib/closeUserAccount";

import { fetchUserTransactions } from "@/lib/fetchUserTransactions";
import { exportUserTransactions } from "@/lib/exportUserTransactions";
import UserTransactionPagination from "./user-transaction-pagination";
import UserTransactionFilters, {
  TransactionFilters,
} from "./user-transaction-filters";
import { toast } from "sonner";
import { Skeleton } from "@/components/ui/skeleton";
import Image from "next/image";
import CloseAccountDialog from "./CloseAccountDialog";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import GrowthChart from "./growth-chart";
import { countryNameToCodeMap } from "@/lib/utils";
import {
  fetchMemberGivingAnalytics,
  GrowthChartDataPoint,
} from "@/lib/fetchMemberGivingAnalytics";

const currencyToCountryCodeMap: Record<string, string> = {
  NGN: "NG",
  GBP: "GB",
  USD: "US",
  EUR: "EU",
  CAD: "CA",
};

// Helper function to format currency
const formatCurrency = (value: number, currencyCode: string) => {
  try {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      currencyDisplay: "narrowSymbol",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(value);
  } catch (error) {
    console.warn(`Failed to format currency ${currencyCode}:`, error);
    return `${currencyCode} ${value.toFixed(2)}`;
  }
};

// Helper function to get status color
const getStatusColor = (status: string) => {
  switch (status?.toUpperCase()) {
    case "SUCCESS":
    case "SUCCESSFUL":
    case "COMPLETED":
      return "bg-green-100 text-green-800 hover:bg-green-200";
    case "PENDING":
    case "PROCESSING":
      return "bg-yellow-100 text-yellow-800 hover:bg-yellow-200";
    case "FAILED":
    case "DECLINED":
    case "CANCELLED":
      return "bg-red-100 text-red-800 hover:bg-red-200";
    default:
      return "bg-gray-100 text-gray-800 hover:bg-gray-200";
  }
};

interface UserPreviewClientPageProps {
  initialUser: User;
  userId: string;
}

// Renamed Component
export default function UserPreviewClientPage({
  initialUser,
  userId,
}: UserPreviewClientPageProps) {
  const user = initialUser;
  const [isClosingAccount, setIsClosingAccount] = useState(false);
  const [showCloseAccountDialog, setShowCloseAccountDialog] = useState(false);

  // --- State for Giving Filters ---
  const currentYearDt = new Date().getFullYear();
  const currentMonthDt = new Date().getMonth() + 1;
  const [selectedYear, setSelectedYear] = useState<string>(
    currentYearDt.toString()
  );
  const [selectedMonth, setSelectedMonth] = useState<string>(
    currentMonthDt.toString().padStart(2, "0")
  );
  const [selectedCurrency, setSelectedCurrency] = useState<string>("NGN");

  const [growthCurrency, setGrowthCurrency] = useState<string>("NGN");
  const [selectedPeriod, setSelectedPeriod] = useState<
    "day" | "week" | "month" | "year"
  >("day");

  const componentId =
    "user-preview-" + Math.random().toString(36).substring(2, 9);

  const [currentPage, setCurrentPage] = useState(1);
  const [isExporting, setIsExporting] = useState(false);
  const [transactionFilters, setTransactionFilters] =
    useState<TransactionFilters>({
      type: "all_types",
      startDate: undefined,
      endDate: undefined,
    });
  const itemsPerPage = 5;

  const yearOptions = Array.from({ length: 5 }, (_, i) =>
    (currentYearDt - i).toString()
  );
  const monthOptions = Array.from({ length: 12 }, (_, i) => ({
    value: (i + 1).toString().padStart(2, "0"),
    label: new Date(0, i).toLocaleString("default", { month: "short" }),
  }));

  const selectedMonthLabel =
    monthOptions.find((m) => m.value === selectedMonth)?.label || selectedMonth;

  const givingTypeColors: Record<string, string> = {
    Offering: "text-green-600",
    Tithe: "text-purple-600",
    Partnership: "text-cyan-600",
    "First-fruit": "text-yellow-600",
    Seed: "text-orange-600",
    "Prophet Offering": "text-pink-600",
    default: "text-gray-700",
  };

  // Fetch member giving analytics data
  const {
    data: growthChartData,
    isLoading: isLoadingGrowthData,
    isError: isGrowthDataError,
    error: growthDataError,
  } = useQuery<GrowthChartDataPoint[]>({
    queryKey: ["memberGivingAnalytics", userId, growthCurrency, selectedPeriod],
    queryFn: () =>
      fetchMemberGivingAnalytics({
        memberId: userId,
        currency: growthCurrency,
        period: selectedPeriod,
      }),
    enabled: !!userId,
  });

  // --- Fetch User Wallets Query ---
  const {
    data: walletsData,
    isLoading: isLoadingWallets,
    isError: isWalletsError,
    error: walletsError,
  } = useQuery<{ wallets: Wallet[] }>({
    queryKey: ["userWallets", userId],
    queryFn: () => fetchUserWallets(userId),
    enabled: !!userId,
  });

  // Close account mutation
  const { mutate: submitCloseAccount, isPending: isClosingAccountMutation } =
    useMutation({
      mutationFn: () => closeUserAccount(userId),
      onSuccess: (data) => {
        toast.success(data.message || "Account closed successfully");
        window.location.href = "/users";
      },
      onError: (error) => {
        toast.error(
          error instanceof Error ? error.message : "Failed to close account"
        );
        console.error("Close account error:", error);
        setIsClosingAccount(false);
      },
    });

  // Get available currencies from user wallets
  const availableCurrencies = useMemo(() => {
    const currencies = new Set<string>();
    currencies.add("NGN");

    // Add currencies from wallets
    if (walletsData?.wallets) {
      walletsData.wallets.forEach((wallet) => {
        if (wallet.currency) {
          currencies.add(wallet.currency);
        }
      });
    }

    return Array.from(currencies);
  }, [walletsData]);

  // --- Fetch Total Giving Analytics Query (Using State) ---
  const {
    data: givingData,
    isLoading: isLoadingGiving,
    isError: isGivingError,
    error: givingError,
  } = useQuery<TotalGivingAnalyticsItem[]>({
    queryKey: [
      "userTotalGiving",
      userId,
      selectedMonth,
      selectedYear,
      selectedCurrency,
    ],
    queryFn: () =>
      fetchTotalGivingAnalytics({
        userId,
        month: selectedMonth,
        year: selectedYear,
        currency: selectedCurrency,
      }),
    enabled: !!userId,
    placeholderData: (previousData) => previousData,
  });

  // Fetch user transactions
  const {
    data: transactionsData,
    isLoading: isLoadingTransactions,
    isError: isTransactionsError,
    error: transactionsError,
  } = useQuery<{ transactions: UserTransaction[] }>({
    queryKey: ["userTransactions", userId],
    queryFn: () => fetchUserTransactions(userId),
    enabled: !!userId,
    placeholderData: (previousData) => previousData,
  });

  // Memoize transactions to avoid re-renders
  const transactions = useMemo(() => {
    return transactionsData?.transactions || [];
  }, [transactionsData]);

  // Get unique transaction types for filter dropdown
  const transactionTypes = useMemo(() => {
    const types = new Set<string>();
    transactions.forEach((transaction) => {
      if (transaction.transaction_type_string) {
        types.add(transaction.transaction_type_string);
      }
    });
    return Array.from(types);
  }, [transactions]);

  // Apply filters to transactions
  const filteredTransactions = useMemo(() => {
    return transactions.filter((transaction) => {
      if (transactionFilters.type !== "all_types") {
        if (transactionFilters.type.startsWith("Offering_")) {
          if (transaction.transaction_type_string !== "Offering") {
            return false;
          }
        } else if (
          transaction.transaction_type_string !== transactionFilters.type
        ) {
          return false;
        }
      }

      // Filter by date range
      if (transactionFilters.startDate) {
        const transactionDate = new Date(transaction.transaction_date);
        const startDate = new Date(transactionFilters.startDate);
        startDate.setHours(0, 0, 0, 0);

        if (transactionDate < startDate) {
          return false;
        }

        if (transactionFilters.endDate) {
          const endDate = new Date(transactionFilters.endDate);
          endDate.setHours(23, 59, 59, 999);

          if (transactionDate > endDate) {
            return false;
          }
        }
      }

      return true;
    });
  }, [transactions, transactionFilters]);

  const totalTransactions = filteredTransactions.length;
  const totalPages = Math.ceil(totalTransactions / itemsPerPage);

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [transactionFilters]);

  // Get current page transactions
  const currentTransactions = useMemo(() => {
    const indexOfLastTransaction = currentPage * itemsPerPage;
    const indexOfFirstTransaction = indexOfLastTransaction - itemsPerPage;
    return filteredTransactions.slice(
      indexOfFirstTransaction,
      indexOfLastTransaction
    );
  }, [filteredTransactions, currentPage, itemsPerPage]);

  if (!user) {
    return (
      <main className="container mx-auto px-6 py-6 space-y-6">
        <p>User not found.</p>
        <Link href="/users">
          <Button variant="outline">Back to Users</Button>
        </Link>
      </main>
    );
  }

  // Handle page change
  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  // Handle filter changes
  const handleFiltersChange = (filters: TransactionFilters) => {
    setTransactionFilters(filters);
  };

  // Handle export transactions
  const handleExportTransactions = async () => {
    if (!userId) return;

    try {
      setIsExporting(true);
      const blob = await exportUserTransactions(userId);
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute(
        "download",
        `${user.name.replace(/\s+/g, "_")}_transactions.csv`
      );
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      toast.success("Transactions exported successfully");
    } catch (error) {
      toast.error("Failed to export transactions");
      console.error("Export error:", error);
    } finally {
      setIsExporting(false);
    }
  };

  // Handle close account button click
  const handleCloseAccount = () => {
    if (!userId) return;
    setShowCloseAccountDialog(true);
  };

  // Handle confirm close account
  const confirmCloseAccount = () => {
    setIsClosingAccount(true);
    submitCloseAccount();
  };

  const userCountryCode =
    countryNameToCodeMap[user.location.country?.toLowerCase() || ""] || "XX";
  const userFlagPath = `/flags/3x2/${userCountryCode}.svg`;

  return (
    <main className="container mx-auto px-6 py-6 space-y-6">
      {/* Close Account Dialog */}
      <CloseAccountDialog
        isOpen={showCloseAccountDialog}
        onClose={() => !isClosingAccount && setShowCloseAccountDialog(false)}
        onConfirm={confirmCloseAccount}
        userName={user.name}
        isClosing={isClosingAccount}
      />

      {/* Breadcrumb */}

      {/* User Profile Section */}
      <Card className="overflow-hidden">
        <div className="p-6">
          <div className="flex items-center text-sm text-gray-500 mb-4">
            <Link href="/users" className="hover:text-gray-700">
              USERS
            </Link>
            <span className="mx-2">/</span>
            <span className="text-gray-400">{user.name}</span>
          </div>
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
            <div className="flex items-center mb-4 md:mb-0">
              <Avatar className="h-16 w-16 mr-4 bg-[#e0dbfc]">
                <AvatarFallback asChild>
                  <Image
                    src="/avatar.svg"
                    alt="User Avatar"
                    width={100}
                    height={100}
                  />
                </AvatarFallback>
              </Avatar>
              <div className="">
                <div className="flex items-center">
                  <h2 className="text-xl font-bold mr-2">{user.name}</h2>
                  <Image
                    src={userFlagPath}
                    alt={`${user.location.country || "User"} flag`}
                    width={20}
                    height={20}
                    className="w-5 h-auto"
                    title={user.location.country || "User Country"}
                    onError={(e) => {
                      e.currentTarget.style.display = "none";
                    }}
                  />
                </div>
                <div className="flex items-center text-sm text-gray-500">
                  <span>{user.email}</span>
                </div>
              </div>
            </div>
            <div className="flex space-x-3">
              {/* <Button
                variant="outline"
                className="flex items-center space-x-2"
                disabled
              >
                <Download className="h-4 w-4" />
                <span>Download Statement</span>
              </Button> */}
              <Button
                variant="outline"
                className="text-red-500 border-red-500 hover:bg-red-50"
                onClick={handleCloseAccount}
                disabled={isClosingAccount || isClosingAccountMutation}
              >
                {isClosingAccount || isClosingAccountMutation ? (
                  <>Closing Account...</>
                ) : (
                  <>Close Account</>
                )}
              </Button>
            </div>
          </div>

          {/* User Details */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                Registered On
              </h3>
              <p className="text-sm">{user.registeredOn}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                Phone Number
              </h3>
              <p className="text-sm">{user.phone}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                Location
              </h3>
              <p className="text-sm">
                {user.location.city}, {user.location.state} (
                {user.location.branch})
              </p>
              <p className="text-xs text-gray-500">{user.location.country}</p>
            </div>
            <div>
              <h3 className="text-sm font-medium text-gray-500 mb-1">
                Address
              </h3>
              <p className="text-sm">{user.location.address}</p>
            </div>
          </div>
        </div>

        {/* Wallets Section */}

        <div className="p-6 grid grid-cols-1 lg:grid-cols-3 gap-6">
          <Card className="bg-[#F8F8F8] rounded-xl lg:col-span-2">
            <CardHeader>
              {/* Updated Header Style */}
              <CardTitle className="flex items-center text-sm font-medium text-gray-700 tracking-wide">
                {/* Replace WalletCards with an actual icon import */}
                <Image
                  src="/wallets.svg"
                  alt="Wallet Icon"
                  width={24}
                  height={24}
                  className="mr-2"
                />
                WALLET
              </CardTitle>
            </CardHeader>
            <CardContent>
              {isLoadingWallets ? (
                // Updated Skeleton layout
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 col-span-2 gap-4">
                  {/* Match card height/style */}
                  <Skeleton className="h-[150px] w-full rounded-xl" />
                  <Skeleton className="h-[150px] w-full rounded-xl" />
                  <Skeleton className="h-[150px] w-full rounded-xl" />
                </div>
              ) : isWalletsError ? (
                <p className="text-sm text-destructive text-center py-4">
                  Error loading wallets:{" "}
                  {(walletsError as Error)?.message || "Unknown error"}
                </p>
              ) : walletsData?.wallets && walletsData.wallets.length > 0 ? (
                // Updated Grid layout to match target UI (adjust cols as needed)
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 col-span-2 gap-4">
                  {/* Add deduplication by currency */}
                  {Object.values(
                    walletsData.wallets.reduce((unique, wallet) => {
                      // Only keep the wallet with the highest balance for each currency
                      if (!unique[wallet.currency] || unique[wallet.currency].balance < wallet.balance) {
                        unique[wallet.currency] = wallet;
                      }
                      return unique;
                    }, {} as Record<string, Wallet>)
                  ).map((wallet) => {
                    // Get country code from currency for the flag
                    const countryCode = currencyToCountryCodeMap[wallet.currency] || "XX";
                    const flagPath = `/flags/3x2/${countryCode}.svg`;
                    
                    // Determine if this wallet should have the highlight border
                    const isHighlighted = wallet.currency === "NGN";

                    return (
                      <div
                        key={wallet.id}
                        className={`
                          bg-white p-5 rounded-xl border
                          flex flex-col items-center text-center
                          transition-all duration-200 ease-in-out hover:shadow-md
                          ${isHighlighted ? "border-purple-500 border-2 shadow-lg" : "border-gray-100"}
                        `}
                      >
                        {/* Flag Image */}
                        <Image
                          width={64}
                          height={64}
                          src={flagPath}
                          alt={`${wallet.currency} flag`}
                          className="w-16 h-16 rounded-full object-cover mb-4 border-2 border-gray-50" // Adjusted size, added subtle border
                          // Hide the img tag if the flag SVG doesn't load
                          onError={(e) => {
                            e.currentTarget.style.display = "none";
                            // Optionally show a placeholder div/icon here
                          }}
                        />
                        {/* Balance Amount */}
                        <p
                          className="text-xl font-semibold text-gray-900 mb-1 truncate"
                          title={formatCurrency(
                            wallet.balance,
                            wallet.currency
                          )}
                        >
                          {formatCurrency(wallet.balance, wallet.currency)}
                        </p>
                        {/* "Balance" Label */}
                        <p className="text-xs text-gray-500">Balance</p>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <p className="text-sm text-gray-500 text-center py-4">
                  No wallets found for this user.
                </p>
              )}
            </CardContent>
          </Card>

          <Card className="overflow-hidden bg-[#F8F8F8] rounded-xl">
            <div className="p-6">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <div className="w-6 h-6 bg-[#f7f9e1] rounded-full flex items-center justify-center mr-2">
                    <svg
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path
                        d="M23 6l-9.5 9.5-5-5L1 18"
                        stroke="#b0c239"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                      <path
                        d="M17 6h6v6"
                        stroke="#b0c239"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      />
                    </svg>
                  </div>
                  <h3 className="text-sm font-medium">GROWTH ANALYSIS</h3>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm"></span>
                  <Select
                    value={selectedPeriod}
                    onValueChange={(value) => {
                      // Update the state with the new period
                      setSelectedPeriod(
                        value as "day" | "week" | "month" | "year"
                      );
                    }}
                  >
                    <SelectTrigger className="w-[80px]">
                      <SelectValue placeholder="Day" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="day">Day</SelectItem>
                      <SelectItem value="week">Week</SelectItem>
                      <SelectItem value="month">Month</SelectItem>
                      <SelectItem value="year">Year</SelectItem>
                    </SelectContent>
                  </Select>

                  <span className="text-sm ml-2">Currency:</span>
                  <Select
                    value={growthCurrency}
                    onValueChange={setGrowthCurrency}
                  >
                    <SelectTrigger className="w-[80px]">
                      <SelectValue placeholder="NGN" />
                    </SelectTrigger>
                    <SelectContent>
                      {availableCurrencies.map((currency) => (
                        <SelectItem key={currency} value={currency}>
                          {currency}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="h-[200px]">
                {isLoadingGrowthData ? (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-sm text-gray-500">
                      Loading growth data...
                    </p>
                  </div>
                ) : isGrowthDataError ? (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-sm text-red-500">
                      Error loading growth data:{" "}
                      {growthDataError instanceof Error
                        ? growthDataError.message
                        : "Unknown error"}
                    </p>
                  </div>
                ) : growthChartData && growthChartData.length > 0 ? (
                  <GrowthChart data={growthChartData} />
                ) : (
                  <div className="flex items-center justify-center h-full">
                    <p className="text-sm text-gray-500">
                      No growth data available
                    </p>
                  </div>
                )}
              </div>
            </div>
          </Card>
        </div>
      </Card>

      {/* Transactions and Givings Section */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-3 h-[470px]">
        {/* Transactions Section - Placeholder/Empty State */}
        <Card className="overflow-y-auto lg:col-span-2">
          <div className="p-6">
            <div className="flex justify-between items-center mb-6">
              <div className="flex items-center space-x-4">
                <h2 className="text-lg font-medium">TRANSACTIONS</h2>
                <span className="text-sm text-gray-500">
                  {totalTransactions}
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <UserTransactionFilters
                  filters={transactionFilters}
                  onFiltersChange={handleFiltersChange}
                  transactionTypes={transactionTypes.map((type) =>
                    type === "Offering" ? "Offering" : type
                  )}
                  key={`transaction-filters-${componentId}`}
                />
                <Button
                  onClick={handleExportTransactions}
                  disabled={isExporting || totalTransactions === 0}
                  className="flex items-center rounded-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  <span className="hidden sm:inline">Export</span>
                </Button>
              </div>
            </div>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="text-xs font-medium text-gray-500">
                      TRANSACTION ID
                    </TableHead>
                    <TableHead className="text-xs font-medium text-gray-500">
                      TYPE
                    </TableHead>
                    <TableHead className="text-xs font-medium text-gray-500">
                      AMOUNT
                    </TableHead>
                    <TableHead className="text-xs font-medium text-gray-500">
                      DATE
                    </TableHead>
                    <TableHead className="text-xs font-medium text-gray-500">
                      STATUS
                    </TableHead>
                    <TableHead className="text-xs font-medium text-gray-500"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {isLoadingTransactions ? (
                    // Loading state
                    Array.from({ length: itemsPerPage }).map((_, index) => (
                      <TableRow key={`loading-${index}`}>
                        <TableCell>
                          <Skeleton className="h-4 w-24" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-16" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-20" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-24" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-16" />
                        </TableCell>
                        <TableCell>
                          <Skeleton className="h-4 w-8" />
                        </TableCell>
                      </TableRow>
                    ))
                  ) : isTransactionsError ? (
                    // Error state
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center">
                          <div className="bg-red-100 rounded-full p-3 mb-3">
                            <Search className="h-6 w-6 text-red-400" />
                          </div>
                          <h3 className="text-lg font-medium mb-1 text-destructive">
                            Error Loading Transactions
                          </h3>
                          <p className="text-sm text-gray-500">
                            {(transactionsError as Error)?.message ||
                              "Could not fetch transactions."}
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : transactions.length === 0 ? (
                    // Empty state
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-8">
                        <div className="flex flex-col items-center justify-center">
                          <div className="bg-gray-100 rounded-full p-3 mb-3">
                            <Search className="h-6 w-6 text-gray-400" />
                          </div>
                          <h3 className="text-lg font-medium mb-1">
                            No Transactions Found
                          </h3>
                          <p className="text-sm text-gray-500">
                            This user has not made any transactions yet.
                          </p>
                        </div>
                      </TableCell>
                    </TableRow>
                  ) : (
                    // Data display
                    currentTransactions.map((transaction: UserTransaction) => (
                      <TableRow key={transaction.id}>
                        <TableCell className="font-medium">
                          {transaction.transaction_id.substring(0, 15)}...
                        </TableCell>
                        <TableCell>
                          {transaction.transaction_type_string}
                        </TableCell>
                        <TableCell>
                          {formatCurrency(
                            parseFloat(transaction.amount_paid),
                            transaction.currency
                          )}
                        </TableCell>
                        <TableCell>
                          {new Date(
                            transaction.transaction_date
                          ).toLocaleDateString()}
                        </TableCell>
                        <TableCell>
                          <Badge
                            className={`${getStatusColor(
                              transaction.transaction_status_string
                            )}`}
                          >
                            {transaction.transaction_status_string}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8"
                          >
                            <ExternalLink className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  )}
                </TableBody>
              </Table>

              {/* Pagination */}
              {!isLoadingTransactions &&
                !isTransactionsError &&
                transactions.length > 0 && (
                  <UserTransactionPagination
                    currentPage={currentPage}
                    totalPages={totalPages}
                    onPageChange={handlePageChange}
                  />
                )}
            </div>
          </div>
        </Card>

        {/* Givings Section - Updated with Filters and Grid Layout */}
        <Card className="overflow-y-auto">
          <div className="p-6">
            {/* Title and Filters */}
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-lg font-medium">GIVINGS</h2>
              <div className="flex items-center space-x-2">
                {/* Year Select */}
                <Select value={selectedYear} onValueChange={setSelectedYear}>
                  <SelectTrigger className="w-[80px] h-8 text-xs">
                    <SelectValue placeholder="Year" />
                  </SelectTrigger>
                  <SelectContent>
                    {yearOptions.map((year) => (
                      <SelectItem key={year} value={year} className="text-xs">
                        {year}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {/* Month Select */}
                <Select value={selectedMonth} onValueChange={setSelectedMonth}>
                  <SelectTrigger className="w-[80px] h-8 text-xs">
                    <SelectValue placeholder="Month" />
                  </SelectTrigger>
                  <SelectContent>
                    {monthOptions.map((month) => (
                      <SelectItem
                        key={month.value}
                        value={month.value}
                        className="text-xs"
                      >
                        {month.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                {/* Currency Select */}
                <Select
                  value={selectedCurrency}
                  onValueChange={setSelectedCurrency}
                >
                  <SelectTrigger className="w-[80px] h-8 text-xs">
                    <SelectValue placeholder="Currency" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableCurrencies.map((currency) => (
                      <SelectItem
                        key={currency}
                        value={currency}
                        className="text-xs"
                      >
                        {currency}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Giving Data Display Area */}
            {isLoadingGiving ? (
              // Loading Skeleton (Grid of Cards)
              <div className="grid grid-cols-2 gap-4">
                {Array.from({ length: 6 }).map((_, index) => (
                  <Card
                    key={index}
                    className="p-4 bg-white rounded-lg shadow-sm"
                  >
                    <Skeleton className="h-4 w-3/4 mb-2" />
                    <Skeleton className="h-6 w-1/2" />
                  </Card>
                ))}
              </div>
            ) : isGivingError ? (
              // Error State
              <div className="flex flex-col items-center justify-center h-[200px] text-center text-sm">
                <div className="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-3">
                  <BarChart3 className="h-6 w-6 text-red-500" />{" "}
                  {/* Keep icon */}
                </div>
                <h3 className="text-md font-medium text-destructive mb-1">
                  Error Loading Givings
                </h3>
                <p className="text-sm text-gray-500">
                  {(givingError as Error)?.message || "Could not fetch data."}
                </p>
              </div>
            ) : givingData &&
              Array.isArray(givingData) &&
              givingData.length > 0 ? (
              // Data Display State (Grid of Cards)
              <div className="grid grid-cols-2 gap-4">
                {givingData.map((giving, index) => (
                  <Card
                    key={`giving-type-${componentId}-${index}`}
                    className="p-4 bg-white rounded-lg shadow-sm"
                  >
                    <p
                      className={`text-sm mb-1 ${
                        givingTypeColors[giving.payment_type] ||
                        givingTypeColors.default
                      }`}
                    >
                      {giving.payment_type}
                    </p>
                    <p className="text-lg font-semibold text-gray-800">
                      {formatCurrency(
                        parseFloat(giving.total_amount) || 0,
                        selectedCurrency
                      )}
                    </p>
                    {/* Optionally display count if needed:
                     <p className="text-xs text-gray-400 mt-1">Count: {giving.total_count}</p>
                     */}
                  </Card>
                ))}
              </div>
            ) : (
              // Empty State
              <div className="flex flex-col items-center justify-center h-[200px] text-center text-sm">
                <div className="w-12 h-12 bg-gray-100 rounded-full flex items-center justify-center mb-3">
                  <BarChart3 className="h-6 w-6 text-gray-400" />{" "}
                  {/* Keep icon */}
                </div>
                <h3 className="text-md font-medium mb-1">
                  No Givings Data Found
                </h3>
                <p className="text-sm text-gray-500">
                  {/* Use the calculated label */}
                  There is no giving data for {selectedMonthLabel}{" "}
                  {selectedYear} in {selectedCurrency}.
                </p>
                {/* Removed button from empty state */}
              </div>
            )}
          </div>
        </Card>
      </div>
    </main>
  );
}
