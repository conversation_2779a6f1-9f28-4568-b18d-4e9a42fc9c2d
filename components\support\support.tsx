"use client";

import { useState, useMemo, useEffect } from "react";
import { useBranch } from "@/store/branch-provider";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { format } from "date-fns";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Skeleton } from "@/components/ui/skeleton";
import { Textarea } from "@/components/ui/textarea";
import { DatePicker } from "@/components/ui/date-picker";
import { Filter } from "lucide-react";
import { toast } from "sonner";
import { cn } from "@/lib/utils";
import {
  fetchSupportRequests,
  fetchSupportStatuses,
  updateSupportRequest,
} from "@/lib/fetchSupportRequests";
import type {
  UpdateSupportRequestPayload,
  FetchSupportRequestsParams,
} from "@/types/support";

export default function SupportPage() {
  const queryClient = useQueryClient();
  const { selectedBranchId: globalBranchId } = useBranch();

  const [activeTab, setActiveTab] = useState("active");
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const [responseText, setResponseText] = useState("");
  const [selectedStatus, setSelectedStatus] = useState<string>("");

  const [filterStatus, setFilterStatus] = useState<string>("all_statuses");
  const [startDate, setStartDate] = useState<Date | undefined>(undefined);
  const [endDate, setEndDate] = useState<Date | undefined>(undefined);
  const [isFilterOpen, setIsFilterOpen] = useState(false);

  const filterParams = useMemo(() => {
    const params: FetchSupportRequestsParams = {};

    if (filterStatus && filterStatus !== "all_statuses") {
      params.status = filterStatus;
    }

    if (startDate) {
      params.start = format(startDate, "yyyy-MM-dd");
    }

    if (endDate) {
      params.end = format(endDate, "yyyy-MM-dd");
    }

    if (globalBranchId !== null) {
      params.location_id = globalBranchId;
    }

    return params;
  }, [filterStatus, startDate, endDate, globalBranchId]);

  const {
    data: supportData,
    isLoading: isLoadingTickets,
    error: ticketsError,
  } = useQuery({
    queryKey: ["supportRequests", filterParams],
    queryFn: () => fetchSupportRequests(filterParams),
  });

  const { data: statusesData } = useQuery({
    queryKey: ["supportStatuses"],
    queryFn: fetchSupportStatuses,
  });

  const { mutate: updateRequest, isPending: isUpdating } = useMutation({
    mutationFn: ({
      id,
      payload,
    }: {
      id: number | string;
      payload: UpdateSupportRequestPayload;
    }) => updateSupportRequest(id, payload),
    onSuccess: () => {
      toast.success("Support request updated successfully");
      queryClient.invalidateQueries({ queryKey: ["supportRequests"] });
      setIsConfirmDialogOpen(false);
      setResponseText("");
    },
    onError: (error) => {
      toast.error(
        `Failed to update support request: ${
          error instanceof Error ? error.message : "Unknown error"
        }`
      );
    },
  });

  const tickets = useMemo(() => {
    return supportData?.tickets || [];
  }, [supportData]);

  const [selectedTicketId, setSelectedTicketId] = useState<string | null>(null);

  const selectedTicket = useMemo(() => {
    return tickets.find((ticket) => ticket.id === selectedTicketId) || null;
  }, [tickets, selectedTicketId]);

  const users = useMemo(() => {
    const uniqueUsers = new Map();

    tickets.forEach((ticket) => {
      if (!uniqueUsers.has(ticket.user.id)) {
        uniqueUsers.set(ticket.user.id, ticket.user);
      }
    });

    return Array.from(uniqueUsers.values());
  }, [tickets]);

  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);

  const selectedUser = useMemo(() => {
    return users.find((user) => user.id === selectedUserId) || null;
  }, [users, selectedUserId]);

  useEffect(() => {
    if (tickets.length > 0 && !selectedTicketId) {
      setSelectedTicketId(tickets[0].id);
      setSelectedUserId(tickets[0].user.id);
    }
  }, [tickets, selectedTicketId]);

  const filteredTickets = useMemo(() => {
    return tickets.filter((ticket) =>
      activeTab === "active" ? ticket.isActive : !ticket.isActive
    );
  }, [tickets, activeTab]);

  const userTickets = useMemo(() => {
    if (!selectedUserId) return [];
    return filteredTickets.filter(
      (ticket) => ticket.user.id === selectedUserId
    );
  }, [filteredTickets, selectedUserId]);

  const openUpdateDialog = () => {
    if (!selectedTicket) return;
    setResponseText(selectedTicket.response || "");
    setSelectedStatus(selectedTicket.status);
    setIsConfirmDialogOpen(true);
  };

  const handleSubmitUpdate = () => {
    if (!selectedTicket) return;

    const payload: UpdateSupportRequestPayload = {
      response: responseText,
      status: selectedStatus,
    };

    updateRequest({ id: selectedTicket.id, payload });
  };

  const handleUserSelect = (userId: string) => {
    setSelectedUserId(userId);

    const userTicket = filteredTickets.find(
      (ticket) => ticket.user.id === userId
    );

    if (userTicket) {
      setSelectedTicketId(userTicket.id);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Resolved":
        return "bg-[#c5ead1] text-[#51ac32]";
      case "Pending":
        return "bg-[#FFE580] text-black";
      case "In Progress":
        return "bg-[#e0dbfc] text-[#866cef]";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <main className="container mx-auto px-6 py-6">
      <Card className="overflow-y-auto">
        <div className="p-6">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <h2 className="text-lg">SUPPORT</h2>
              {isLoadingTickets ? (
                <Skeleton className="ml-2 h-6 w-10 rounded-full" />
              ) : (
                <span className="ml-2 text-sm bg-gray-100 px-3 py-1 rounded-full">
                  {filteredTickets.length}
                </span>
              )}
            </div>

            <div className="flex items-center space-x-4">
              <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "flex items-center gap-2 rounded-full",
                      ((filterStatus && filterStatus !== "all_statuses") ||
                        startDate ||
                        endDate) &&
                        "bg-[#f8f0ff] border-[#866cef] text-[#866cef]"
                    )}
                  >
                    <Filter className="h-4 w-4" />
                    <span>Filter</span>
                    {((filterStatus && filterStatus !== "all_statuses") ||
                      startDate ||
                      endDate) && (
                      <Badge className="ml-1 bg-[#866cef] text-white">
                        {(filterStatus !== "all_statuses" ? 1 : 0) +
                          (startDate ? 1 : 0) +
                          (endDate ? 1 : 0)}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80 p-0" align="end">
                  <div className="flex flex-col max-h-[80vh]">
                    {/* Header */}
                    <div className="p-4 border-b flex-shrink-0">
                      <h3 className="font-medium">Filter Support Requests</h3>
                    </div>

                    {/* Scrollable content */}
                    <div className="flex-1 p-4 space-y-4 overflow-y-auto">
                      <div className="space-y-2">
                        <label className="text-sm font-medium">Status</label>
                        <Select
                          value={filterStatus}
                          onValueChange={setFilterStatus}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder="Select status" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="all_statuses">
                              All Statuses
                            </SelectItem>
                            {statusesData?.statuses.map((status) => (
                              <SelectItem key={status} value={status}>
                                {status}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">
                          Start Date
                        </label>
                        <DatePicker
                          date={startDate}
                          setDate={setStartDate}
                          placeholder="Select start date"
                        />
                      </div>

                      <div className="space-y-2">
                        <label className="text-sm font-medium">End Date</label>
                        <DatePicker
                          date={endDate}
                          setDate={setEndDate}
                          placeholder="Select end date"
                          disabled={!startDate}
                        />
                      </div>

                      {/* Branch/Location filter removed as requested */}
                    </div>

                    {/* Fixed footer with buttons */}
                    <div className="p-4 border-t flex-shrink-0 flex justify-between">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setFilterStatus("all_statuses");
                          setStartDate(undefined);
                          setEndDate(undefined);
                        }}
                      >
                        Reset
                      </Button>
                      <Button size="sm" onClick={() => setIsFilterOpen(false)}>
                        Apply
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>
          </div>

          <Tabs value={activeTab} onValueChange={setActiveTab} className="mb-6">
            <TabsList className="w-[300px]">
              <TabsTrigger
                value="active"
                className="flex-1 rounded-full data-[state=active]:bg-white data-[state=active]:border data-[state=active]:border-[#866cef] data-[state=active]:text-black"
              >
                ACTIVE
              </TabsTrigger>
              <TabsTrigger
                value="completed"
                className="flex-1 rounded-full data-[state=active]:bg-white data-[state=active]:border data-[state=active]:border-[#866cef] data-[state=active]:text-black"
              >
                COMPLETED
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {isLoadingTickets ? (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-3">
              <div className="lg:col-span-1">
                <div className="space-y-2">
                  <Skeleton className="h-20 w-full rounded-xl" />
                  <Skeleton className="h-20 w-full rounded-xl" />
                  <Skeleton className="h-20 w-full rounded-xl" />
                </div>
              </div>
              <div className="bg-[#F8F8F8] p-6 rounded-xl lg:col-span-3">
                <Skeleton className="h-60 w-full rounded-xl" />
              </div>
            </div>
          ) : ticketsError ? (
            <div className="text-center text-red-500 p-6">
              Error loading support requests. Please try again later.
            </div>
          ) : filteredTickets.length === 0 ? (
            <div className="h-[300px] text-center text-gray-500 p-6">
              No {activeTab === "active" ? "active" : "completed"} support
              requests found.
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-3 overflow-y-auto">
              {/* User List - Takes 1/4 of the width on large screens */}
              <div className="lg:col-span-1">
                <div className="space-y-2 overflow-y-auto">
                  {users.map((user) => {
                    const ticketCount = tickets.filter(
                      (ticket) =>
                        ticket.user.id === user.id &&
                        (activeTab === "active"
                          ? ticket.isActive
                          : !ticket.isActive)
                    ).length;
                    if (ticketCount === 0) return null;

                    return (
                      <div
                        key={user.id}
                        className={cn(
                          "p-4 rounded-xl cursor-pointer",
                          selectedUserId === user.id
                            ? "bg-[#f8f0ff]"
                            : "bg-gray-50 hover:bg-gray-100"
                        )}
                        onClick={() => handleUserSelect(user.id)}
                      >
                        <div className="flex justify-between items-center">
                          <div>
                            <h3 className="font-medium">{user.name}</h3>
                            <p className="text-sm text-gray-500">
                              {user.email || "No email provided"}
                            </p>
                          </div>
                          {ticketCount > 0 && (
                            <Badge className="bg-[#FFCC00] text-black border-none">
                              {ticketCount}
                            </Badge>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Ticket Details - Takes 3/4 of the width on large screens */}
              <div className="bg-[#F8F8F8] p-6 rounded-xl lg:col-span-3 overflow-y-auto">
                {/* User's Tickets List */}
                {userTickets.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium mb-3">
                      {selectedUser?.name}&apos;s Tickets
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-3 mb-6">
                      {userTickets.map((ticket) => (
                        <div
                          key={ticket.id}
                          className={cn(
                            "p-3 rounded-lg cursor-pointer border",
                            selectedTicketId === ticket.id
                              ? "border-[#866cef] bg-[#f8f0ff]"
                              : "border-gray-200 bg-white hover:border-gray-300"
                          )}
                          onClick={() => setSelectedTicketId(ticket.id)}
                        >
                          <div className="flex justify-between items-start mb-2">
                            <h4 className="font-medium">{ticket.title}</h4>
                            <Badge
                              className={cn(
                                "ml-2",
                                getStatusColor(ticket.status)
                              )}
                            >
                              {ticket.status}
                            </Badge>
                          </div>
                          <p className="text-sm text-gray-500 truncate">
                            {ticket.description}
                          </p>
                          <div className="text-xs text-gray-400 mt-2">
                            {ticket.date}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Selected Ticket Details */}
                {selectedTicket ? (
                  <div className="bg-white p-6 rounded-xl">
                    <div className="flex flex-col md:flex-row justify-between items-center mb-8">
                      <div>
                        <h3 className="text-sm text-gray-500 mb-1">
                          Ticket Number
                        </h3>
                        <p>{selectedTicket.ticketNumber}</p>
                      </div>
                      <div>
                        <h3 className="text-sm text-gray-500 mb-1">Date</h3>
                        <p>{selectedTicket.date}</p>
                      </div>
                      <div
                        className={cn(
                          "px-4 py-2 rounded-md",
                          getStatusColor(selectedTicket.status)
                        )}
                      >
                        {selectedTicket.status}
                      </div>
                    </div>

                    <div className="flex justify-between items-center mb-2">
                      <h2 className="text-xl font-bold">
                        {selectedTicket.title}
                      </h2>
                    </div>

                    <p className="text-gray-700 mb-6">
                      {selectedTicket.description}
                    </p>

                    {selectedTicket.attachment && (
                      <div className="mb-6">
                        <h3 className="text-sm text-gray-500 mb-2">
                          Attachment
                        </h3>
                        <a
                          href={selectedTicket.attachment}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-[#866cef] hover:underline"
                        >
                          View Attachment
                        </a>
                      </div>
                    )}

                    {selectedTicket.response && (
                      <div className="mb-6">
                        <h3 className="text-sm text-gray-500 mb-2">Response</h3>
                        <p className="p-3 bg-gray-50 rounded-md">
                          {selectedTicket.response}
                        </p>
                      </div>
                    )}

                    {selectedTicket.isActive && (
                      <div className="flex justify-end">
                        <Button
                          variant="default"
                          className="bg-black text-white hover:bg-gray-800"
                          onClick={openUpdateDialog}
                        >
                          Update Request
                        </Button>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="bg-white p-6 rounded-md border text-center">
                    <p className="text-gray-500">
                      Select a ticket to view details
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </Card>

      {/* Update Request Dialog */}
      <Dialog open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>
        <DialogContent className="sm:max-w-md rounded-xl">
          <DialogHeader className="text-center">
            <div className="mx-auto w-12 h-12 rounded-full bg-[#e0dbfc] flex items-center justify-center mb-4">
              <span className="text-[#866cef] text-xl font-bold">?</span>
            </div>
            <DialogTitle className="text-sm text-center">
              UPDATE SUPPORT REQUEST
            </DialogTitle>
          </DialogHeader>
          <DialogDescription className="text-center text-[#A7A6A6] mb-4">
            Please provide a response to this support request.
          </DialogDescription>

          <div className="space-y-4">
            <div>
              <label
                htmlFor="status"
                className="block text-sm font-medium mb-1"
              >
                Status
              </label>
              <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                <SelectTrigger
                  id="status"
                  className={cn(
                    getStatusColor(selectedStatus),
                    "flex justify-between items-center"
                  )}
                >
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  {statusesData?.statuses.map((status) => (
                    <SelectItem key={status} value={status}>
                      {status}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <label
                htmlFor="response"
                className="block text-sm font-medium mb-1"
              >
                Response
              </label>
              <Textarea
                id="response"
                placeholder="Enter your response here..."
                value={responseText}
                onChange={(e) => setResponseText(e.target.value)}
                className="min-h-[100px]"
              />
            </div>
          </div>

          <DialogFooter className="flex justify-between sm:justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsConfirmDialogOpen(false)}
              className="flex-1 mr-2 rounded-full"
              disabled={isUpdating}
            >
              Cancel
            </Button>
            <Button
              type="button"
              className="flex-1 rounded-full bg-black text-white hover:bg-gray-800"
              onClick={handleSubmitUpdate}
              disabled={isUpdating}
            >
              {isUpdating ? "Updating..." : "Submit Response"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </main>
  );
}
