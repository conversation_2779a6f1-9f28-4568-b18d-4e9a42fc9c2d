// lib/fetchManagers.ts

"use server";
import type { Manager, UpdateManagerPayload } from "@/types/manager";
import { cookies } from "next/headers";

const BASE_URL = process.env.BASE_URL;

// Fetch all managers
export const fetchAllManagers = async (): Promise<{ managers: Manager[] }> => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const url = `${BASE_URL}/admin/managers/all`;

  if (!BASE_URL) {
    throw new Error("API base URL is undefined. Check environment variables.");
  }

  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    cache: "no-store",
  });

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ message: "Failed to fetch managers" }));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  const responseData = await response.json();
  if (
    responseData &&
    responseData.result &&
    Array.isArray(responseData.result)
  ) {
    const managersWithHandledBranches = responseData.result.map(
      (manager: Manager) => ({
        ...manager,
        branches: manager.branches ?? [],
      })
    );
    return { managers: managersWithHandledBranches };
  } else {
    console.error("Unexpected API response structure:", responseData);
    throw new Error("Failed to parse managers from API response.");
  }
};

// Update a specific manager
export const updateManager = async (
  id: string,
  payload: UpdateManagerPayload
): Promise<{ message?: string; manager?: Manager }> => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  if (!token) {
    throw new Error("Authentication token not found.");
  }

  const url = `${BASE_URL}/admin/managers/${id}/update`;

  if (!BASE_URL) {
    throw new Error("API base URL is undefined. Check environment variables.");
  }

  if (payload.role_name) {
    console.log(`Setting role_name to: ${payload.role_name}`);
  }

  const response = await fetch(url, {
    method: "PATCH",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(payload),
  });

  if (!response.ok) {
    const errorData = await response
      .json()
      .catch(() => ({ message: "Failed to update manager" }));
    throw new Error(
      errorData.message || `HTTP error! status: ${response.status}`
    );
  }

  return response.json();
};
