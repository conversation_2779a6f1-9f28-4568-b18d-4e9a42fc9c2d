// lib/fetchUserById.ts
"use server";

import { cookies } from "next/headers";
import { format, isValid, parseISO, getTime } from "date-fns";
import type { ApiUser, SingleUserApiResponse, User } from "@/types/user";
// --- Helper Function to Map API User to UI User ---
function mapApiUserToUser(apiUser: ApiUser): User {
  let formattedDate = "N/A";
  let registrationTimestamp = 0;
  try {
    if (apiUser.created_at) {
      const parsedDate = parseISO(apiUser.created_at);
      if (isValid(parsedDate)) {
        formattedDate = format(parsedDate, "dd/MM/yyyy");
        registrationTimestamp = getTime(parsedDate);
      } else {
        console.warn("Could not parse user date:", apiUser.created_at);
      }
    }
  } catch (e) {
    console.warn("Error parsing user date:", apiUser.created_at, e);
  }

  // Combine first and last name, handle nulls
  const name = `${apiUser.first_name || ""} ${apiUser.last_name || ""}`.trim();

  return {
    id: apiUser.id,
    name: name || "N/A",
    email: apiUser.email || "N/A",
    phone: apiUser.phone || "N/A",
    location: {
      city: apiUser.branch_state || apiUser.state || "N/A",
      state: apiUser.branch_state || apiUser.state || "N/A",
      address: apiUser.address || "N/A",
      country: apiUser.branch_country || apiUser.country || "N/A",
      branch: apiUser.branch || "N/A",
    },
    registeredOn: formattedDate,
    registrationTimestamp: registrationTimestamp,
    wallets: apiUser.wallets || [],
  };
}

// --- The Core Fetching Function ---
export async function fetchUserById(
  userId: number | string
): Promise<{ user: User | null }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  const API_BASE_URL = process.env.BASE_URL;
  const API_URL = `${API_BASE_URL}/admin/members/${userId}`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      if (response.status === 404) {
        console.warn(`User with ID ${userId} not found.`);
        return { user: null };
      }
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }
      console.error(`API Error Response (${response.status}):`, errorData);
      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Check server logs for details.";
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: SingleUserApiResponse = await response.json();

    if (
      data.message !== "success" ||
      !data.result ||
      typeof data.result !== "object"
    ) {
      console.error("Unexpected API response structure for single user:", data);
      throw new Error("Failed to fetch user or invalid data format.");
    }

    // --- Map API data to UI structure ---
    const user = mapApiUserToUser(data.result);

    return { user };
  } catch (error) {
    console.error(`Error fetching user with ID ${userId}:`, error);
    return { user: null };
  }
}
