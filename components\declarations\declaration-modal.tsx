/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import { useState, useEffect } from "react";
import { Dialog, DialogContent, DialogClose } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { X, Bold, Italic, Underline, AlignCenter } from "lucide-react";
import { cn } from "@/lib/utils";
import { DialogTitle } from "@radix-ui/react-dialog";
import Image from "next/image";

const backgroundColorOptions = [
  { name: "<PERSON>l", value: "#60b5c0" },
  { name: "Black", value: "#1e0605" },
  { name: "Purple", value: "#866cef" },
  { name: "Pink", value: "#bfb2f4" },
  { name: "Orange", value: "#ff9100" },
  { name: "Yellow", value: "#f8ce48" },
];

const textColorOptions = [
  { name: "White", value: "#FFFFFF" },
  { name: "Black", value: "#000000" },
  { name: "Light Green", value: "#EDFCB2" },
  { name: "Dark Green", value: "#225541" },
  { name: "Orange", value: "#FF9100" },
  { name: "Red", value: "#FF0000" },
];

interface DeclarationModalProps {
  isOpen: boolean;
  onClose: () => void;
  declaration?: any;
  declarations?: any;
  onSave: (declaration: any) => void;
  isSaving?: boolean;
}

export default function DeclarationModal({
  isOpen,
  onClose,
  declaration,
  declarations,
  onSave,
  isSaving = false,
}: DeclarationModalProps) {
  const [text, setText] = useState("");
  const [backgroundColor, setBackgroundColor] = useState(
    backgroundColorOptions[0].value
  );
  const [textColor, setTextColor] = useState(textColorOptions[0].value);
  const [isBold, setIsBold] = useState(false);
  const [isItalic, setIsItalic] = useState(false);
  const [isUnderline, setIsUnderline] = useState(false);
  const [selectedDecree, setSelectedDecree] = useState<string | null>(null);

  useEffect(() => {
    if (declaration) {
      setText(declaration.text || "");
      setBackgroundColor(
        declaration.background_color ||
          declaration.color ||
          backgroundColorOptions[0].value
      );
      setTextColor(declaration.text_color || textColorOptions[0].value);

      setIsBold(false);
      setIsItalic(false);
      setIsUnderline(false);
    } else {
      setText("");
      setBackgroundColor(backgroundColorOptions[0].value);
      setTextColor(textColorOptions[0].value);
      setIsBold(false);
      setIsItalic(false);
      setIsUnderline(false);
    }
  }, [declaration, isOpen]);

  const handleSave = () => {
    onSave({
      text,
      background_color: backgroundColor,
      text_color: textColor,
      formatting: {
        bold: isBold,
        italic: isItalic,
        underline: isUnderline,
      },
    });
  };

  const handleSelectDecree = (decree: any) => {
    setSelectedDecree(decree.id);
    if (decree.text) {
      setText(decree.text);
    }
  };

  const characterCount = text.length;
  const maxCharacters = 500;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl p-0 flex h-[80vh] gap-0">
        <DialogTitle className="sr-only" />
        <div className="w-64 border-r p-6 flex flex-col h-full">
          <DialogClose className="absolute left-4 top-4 flex items-center">
            <X className="h-4 w-4" />
            <span className="ml-2 text-sm">CLOSE</span>
          </DialogClose>

          <div className="mt-12">
            <h3 className="text-sm text-gray-500 mb-4">DECREES</h3>
            <div className="space-y-2">
              {declarations?.slice(0, 5).map((decree: any) => (
                <div
                  key={decree.id}
                  className={cn(
                    "p-2 rounded cursor-pointer hover:bg-gray-100",
                    selectedDecree === decree.id && "bg-gray-100"
                  )}
                  onClick={() => handleSelectDecree(decree)}
                >
                  <p className="text-sm truncate">{decree.text}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="bg-[#FBFBFB] flex w-full rounded-lg overflow-hidden">
          <div className="flex-1 mt-16 p-5 flex flex-col">
            {/*  */}
            <div className="relative w-full mb-4 rounded-lg">
              <Textarea
                value={text}
                onChange={(e) => setText(e.target.value)}
                placeholder="WRITE A DECREE"
                className="w-full shadow bg-white min-h-[260px] pb-16 resize-none rounded-lg border-none focus:ring-none focus:outline-none"
                maxLength={maxCharacters}
              />
              <div className="w-[328px] absolute rounded-b-lg py-2 bottom-0 bg-[#F2F2F2] flex justify-between mt-2 items-center">
                <div className="flex items-center ml-2">
                  <Image
                    src="/ellipse.svg"
                    alt="Heart"
                    width={10}
                    height={10}
                    className="flex items-center"
                  />
                  <span className="text-xs text-gray-500 ml-1">
                    {characterCount}/{maxCharacters}
                  </span>
                </div>

                <button
                  className="mr-2 rounded-full bg-gray-100 border px-5 py-1.5 text-sm text-gray-600"
                  onClick={() => setText("")}
                >
                  Clear
                </button>
              </div>
            </div>
          </div>

          <div className="w-[400px] my-3 mx-3 flex-1 bg-[#F2F2F2] p-6 flex flex-col rounded-lg">
            <div className="bg-white py-2 rounded-full flex justify-between items-center mb-4">
              <div className="flex items-center ml-2">
                <Button variant="ghost" size="icon" className="rounded-full">
                  <AlignCenter className="h-4 w-4" />
                </Button>
              </div>

              <div className="flex items-center bg-[#F2F2F2] rounded-full overflow-hidden">
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "rounded-none text-sm",
                    isBold && "bg-gray-200"
                  )}
                  onClick={() => setIsBold(!isBold)}
                >
                  <Bold className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "rounded-none text-sm",
                    isItalic && "bg-gray-200"
                  )}
                  onClick={() => setIsItalic(!isItalic)}
                >
                  <Italic className="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "rounded-none text-sm",
                    isUnderline && "bg-gray-200"
                  )}
                  onClick={() => setIsUnderline(!isUnderline)}
                >
                  <Underline className="h-4 w-4" />
                </Button>
              </div>

              <Button
                className="rounded-full mr-2"
                onClick={handleSave}
                disabled={isSaving}
              >
                {isSaving ? "Publishing..." : "Publish"}{" "}
                {/* Change text when saving */}
              </Button>
            </div>

            {/* Background Color Selection */}
            <h4 className="text-xs text-gray-500 mb-2 text-center">
              BACKGROUND COLOR
            </h4>
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              {backgroundColorOptions.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    "w-8 h-8 rounded-full cursor-pointer border border-gray-300",
                    backgroundColor === option.value &&
                      "ring-2 ring-offset-2 ring-black"
                  )}
                  style={{ backgroundColor: option.value }}
                  onClick={() => setBackgroundColor(option.value)}
                  title={option.name}
                />
              ))}
            </div>

            {/* Text Color Selection */}
            <h4 className="text-xs text-gray-500 mb-2 text-center">
              TEXT COLOR
            </h4>
            <div className="flex flex-wrap justify-center gap-2 mb-4">
              {textColorOptions.map((option) => (
                <div
                  key={option.value}
                  className={cn(
                    "w-8 h-8 rounded-full cursor-pointer border border-gray-300 flex items-center justify-center",
                    textColor === option.value &&
                      "ring-2 ring-offset-2 ring-black"
                  )}
                  style={{ backgroundColor: option.value }}
                  onClick={() => setTextColor(option.value)}
                  title={option.name}
                >
                  {/* Optional: Add a contrasting indicator if color is light */}
                  {option.value.toUpperCase() === "#FFFFFF" && (
                    <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                  )}
                </div>
              ))}
            </div>

            {/* Preview Area */}
            <h4 className="text-xs text-gray-500 mb-2 text-center">PREVIEW</h4>
            <div
              className="flex-1 rounded-lg p-6 flex flex-col overflow-auto"
              style={{ backgroundColor: backgroundColor }}
            >
              <div
                style={{ color: textColor }}
                className={cn(
                  "text-xl break-words whitespace-pre-wrap",
                  isBold && "font-bold",
                  isItalic && "italic",
                  isUnderline && "underline"
                )}
              >
                {text ? (
                  text.split("\n\n").map((paragraph, index) => (
                    <p key={index} className="mb-6 uppercase">
                      {paragraph}
                    </p>
                  ))
                ) : (
                  <p className="">Preview will appear here...</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
