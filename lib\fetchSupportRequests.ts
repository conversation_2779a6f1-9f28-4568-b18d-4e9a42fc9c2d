"use server";

import { cookies } from "next/headers";
import { format, parseISO } from "date-fns";
import type {
  SupportRequest,
  SupportRequestsApiResponse,
  SupportStatusApiResponse,
  UpdateSupportRequestApiResponse,
  FetchSupportRequestsParams,
  UpdateSupportRequestPayload,
  SupportTicket,
} from "@/types/support";

const BASE_URL = process.env.BASE_URL;

function transformSupportRequest(request: SupportRequest): SupportTicket {
  const formattedDate = format(parseISO(request.created_at), "dd/MM/yyyy");

  const isActive = !["Completed", "Cancelled"].includes(request.status);

  const ticketNumber = `ST-${request.id.toString().padStart(7, "0")}`;

  return {
    id: request.id.toString(),
    ticketNumber,
    date: formattedDate,
    status: request.status,
    title: request.subject,
    description: request.message,
    response: request.response,
    attachment: request.attachment,
    user: {
      id: request.member_id,
      name: `${request.first_name} ${request.last_name}`,
      email: "", // Email not provided in API response
      ticketCount: 0, // Will be calculated later
    },
    isActive,
    created_at: request.created_at,
  };
}

// Fetch all support requests
export async function fetchSupportRequests(
  params: FetchSupportRequestsParams = {}
): Promise<{ tickets: SupportTicket[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  const searchParams = new URLSearchParams();

  if (params.status) {
    searchParams.append("status", params.status);
  }

  if (params.start) {
    searchParams.append("start", params.start);
  }

  if (params.end) {
    searchParams.append("end", params.end);
  }

  if (params.location_id !== undefined) {
    searchParams.append("location_id", params.location_id.toString());
  }

  const API_URL = `${BASE_URL}/admin/requests${
    searchParams.toString() ? `?${searchParams.toString()}` : ""
  }`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }

      console.error(
        `API Error fetching support requests (${response.status}):`,
        errorData
      );

      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to fetch support requests.";

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: SupportRequestsApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error(
        "Unexpected API response structure for support requests:",
        data
      );
      throw new Error("Invalid data format received for support requests.");
    }

    // Transform API data to UI format
    let tickets = data.result.map(transformSupportRequest);

    // Calculate ticket count per user
    const userTicketCounts = new Map<string, number>();
    tickets.forEach((ticket) => {
      const userId = ticket.user.id;
      userTicketCounts.set(userId, (userTicketCounts.get(userId) || 0) + 1);
    });

    // Update ticket counts
    tickets = tickets.map((ticket) => ({
      ...ticket,
      user: {
        ...ticket.user,
        ticketCount: userTicketCounts.get(ticket.user.id) || 0,
      },
    }));

    return { tickets };
  } catch (error) {
    console.error("Error in fetchSupportRequests:", error);
    throw error;
  }
}

// Fetch available support request statuses
export async function fetchSupportStatuses(): Promise<{ statuses: string[] }> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  const API_URL = `${BASE_URL}/admin/request-status`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }

      console.error(
        `API Error fetching support statuses (${response.status}):`,
        errorData
      );

      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to fetch support statuses.";

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: SupportStatusApiResponse = await response.json();

    if (data.message !== "success" || !Array.isArray(data.result)) {
      console.error(
        "Unexpected API response structure for support statuses:",
        data
      );
      throw new Error("Invalid data format received for support statuses.");
    }

    return { statuses: data.result };
  } catch (error) {
    console.error("Error in fetchSupportStatuses:", error);
    throw error;
  }
}

// Update a support request
export async function updateSupportRequest(
  id: number | string,
  payload: UpdateSupportRequestPayload
): Promise<UpdateSupportRequestApiResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  const API_URL = `${BASE_URL}/admin/requests/${id}/update`;

  try {
    const response = await fetch(API_URL, {
      method: "PATCH",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
        accept: "application/json",
      },
      body: JSON.stringify(payload),
    });

    const data: UpdateSupportRequestApiResponse = await response.json();

    if (!response.ok) {
      console.error(
        `API Error updating support request ${id} (${response.status}):`,
        data || "No response body"
      );
      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${
          data?.message || "Failed to update support request."
        }`
      );
    }

    if (data.message !== "success" || data.result !== "success") {
      console.warn(
        "Support request updated, but response structure might be unexpected."
      );
    }

    return data;
  } catch (error) {
    console.error(`Error in updateSupportRequest for ID ${id}:`, error);
    if (error instanceof Error && error.message.startsWith("API Error:")) {
      throw error;
    }
    throw new Error(
      "Could not update support request. Please try again later."
    );
  }
}
