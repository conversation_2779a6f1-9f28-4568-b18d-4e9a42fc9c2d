"use client";

import React, { useEffect } from "react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useBranch } from "@/store/branch-provider";

interface BranchSelectorProps {
  value: number | null;
  onChange: (value: number | null) => void;
  className?: string;
}

export default function BranchSelector({
  value,
  onChange,
  className = "",
}: BranchSelectorProps) {
  const { availableBranches, isLoading, error } = useBranch();

  // Set the first branch as default if none is selected
  useEffect(() => {
    if (
      value === null &&
      availableBranches.length > 0 &&
      !isLoading &&
      !error
    ) {
      onChange(availableBranches[0].id);
    }
  }, [availableBranches, value, onChange, isLoading, error]);

  if (isLoading) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Loading branches..." />
        </SelectTrigger>
      </Select>
    );
  }

  if (error) {
    return (
      <Select disabled>
        <SelectTrigger className={className}>
          <SelectValue placeholder="Error loading branches" />
        </SelectTrigger>
      </Select>
    );
  }

  return (
    <Select
      value={value?.toString() || "all_branches"}
      onValueChange={(val) =>
        onChange(val && val !== "all_branches" ? parseInt(val, 10) : null)
      }
    >
      <SelectTrigger className={className}>
        <SelectValue placeholder="Select a branch" />
      </SelectTrigger>
      <SelectContent>
        <SelectItem value="all_branches">All Branches</SelectItem>
        {availableBranches.map((branch) => (
          <SelectItem key={branch.id} value={branch.id.toString()}>
            {branch.name}
          </SelectItem>
        ))}
      </SelectContent>
    </Select>
  );
}
