// components/transactions/TransactionPagination.tsx
"use client";

import type React from "react";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface TransactionPaginationProps {
  page: number | null;
  totalPages: number;
  setPage: (page: number) => void;
}

export default function TransactionPagination({
  page,
  totalPages,
  setPage,
}: TransactionPaginationProps) {
  const getPaginationItems = () => {
    const items = [];
    const maxVisiblePages = 5;
    const currentPage = page ?? 1;

    // Always show first page
    items.push(
      <Button
        key={1}
        variant={currentPage === 1 ? "default" : "outline"}
        size="icon"
        className={`h-8 w-8 rounded-full ${
          currentPage === 1 ? "bg-[#866cef]" : ""
        }`}
        onClick={() => setPage(1)}
      >
        1
      </Button>
    );

    const startPage = Math.max(
      2,
      currentPage - Math.floor(maxVisiblePages / 2)
    );
    const endPage = Math.min(totalPages - 1, startPage + maxVisiblePages - 3);

    if (startPage > 2) {
      items.push(
        <span key="ellipsis1" className="px-2">
          ...
        </span>
      );
    }

    for (let i = startPage; i <= endPage; i++) {
      items.push(
        <Button
          key={i}
          variant={currentPage === i ? "default" : "outline"}
          size="icon"
          className={`h-8 w-8 rounded-full ${
            currentPage === i ? "bg-[#866cef] " : ""
          }`}
          onClick={() => setPage(i)}
        >
          {i}
        </Button>
      );
    }

    if (endPage < totalPages - 1) {
      items.push(
        <span key="ellipsis2" className="px-2">
          ...
        </span>
      );
    }

    // Always show last page if totalPages > 1
    if (totalPages > 1) {
      items.push(
        <Button
          key={totalPages}
          variant={currentPage === totalPages ? "default" : "outline"}
          size="icon"
          className={`h-8 w-8 rounded-full ${
            currentPage === totalPages ? "bg-[#866cef]" : ""
          }`}
          onClick={() => setPage(totalPages)}
        >
          {totalPages}
        </Button>
      );
    }

    return items;
  };

  if (totalPages <= 1) {
    return null;
  }

  return (
    <div className="flex justify-center items-center mt-6 space-x-2">
      <Button
        variant="outline"
        size="sm"
        onClick={() => setPage(Math.max(1, (page ?? 1) - 1))}
        disabled={(page ?? 1) === 1}
        className="flex items-center"
      >
        <ChevronLeft className="h-4 w-4 mr-1" />
        Previous
      </Button>

      <div className="flex items-center space-x-1">{getPaginationItems()}</div>

      <Button
        variant="outline"
        size="sm"
        onClick={() => setPage(Math.min(totalPages, (page ?? 1) + 1))}
        disabled={(page ?? 1) === totalPages}
        className="flex items-center"
      >
        Next
        <ChevronRight className="h-4 w-4 ml-1" />
      </Button>
    </div>
  );
}
