"use server";

import { cookies } from "next/headers";
import type {
  GivingAnalyticsApiResponse,
  GivingAnalyticsParams,
} from "@/types/giving";

const BASE_URL = process.env.BASE_URL;

export async function fetchGivingAnalytics(
  params: GivingAnalyticsParams = {}
): Promise<GivingAnalyticsApiResponse> {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  const searchParams = new URLSearchParams();

  searchParams.append("currency", params.currency || "NGN");

  if (params.period) {
    searchParams.append("period", params.period);
  }

  if (params.location_id !== undefined) {
    searchParams.append("location_id", params.location_id.toString());
  }

  const API_URL = `${BASE_URL}/admin/giving-analytics?${searchParams.toString()}`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorData: unknown = {};
      try {
        errorData = await response.json();
      } catch (e) {
        console.error("Failed to parse error response JSON:", e);
      }

      console.error(
        `API Error fetching giving analytics (${response.status}):`,
        errorData
      );

      const errorMessage =
        typeof errorData === "object" &&
        errorData !== null &&
        "message" in errorData &&
        typeof errorData.message === "string"
          ? errorData.message
          : "Failed to fetch giving analytics.";

      throw new Error(
        `API Error: ${response.status} ${response.statusText}. ${errorMessage}`
      );
    }

    const data: GivingAnalyticsApiResponse = await response.json();

    if (data.message !== "success" || !data.result) {
      console.error(
        "Unexpected API response structure for giving analytics:",
        data
      );
      throw new Error("Invalid data format received for giving analytics.");
    }
    if (data.result.grouped_data === null) {
      console.log(
        `No data available for currency: ${params.currency}, period: ${
          params.period || "default"
        }`
      );
      return {
        message: data.message,
        result: {
          grouped_data: [],
        },
        status: data.status,
      };
    }

    return data;
  } catch (error) {
    console.error("Error in fetchGivingAnalytics:", error);
    throw error;
  }
}
