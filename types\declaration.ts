export interface Declaration {
  id: number;
  text: string;
  background_color: string;
  text_color: string;
  created_at: string;
}

export interface NewDeclarationPayload {
  background_color: string;
  declarations: string[];
  text_color: string;
}

// Payload for updating a single declaration
export interface UpdateDeclarationPayload {
  text?: string;
  background_color?: string;
  text_color?: string;
}
// Response type for delete declaration
export interface DeleteDeclarationResponse {
  message: string;
  result: string;
  status: number;
}
