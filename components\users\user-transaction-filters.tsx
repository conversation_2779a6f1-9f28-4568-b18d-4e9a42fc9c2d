"use client";

import { useState } from "react";
import { Filter } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { DatePicker } from "@/components/ui/date-picker";
import { Badge } from "@/components/ui/badge";

export interface TransactionFilters {
  type: string;
  startDate: Date | undefined;
  endDate: Date | undefined;
}

interface UserTransactionFiltersProps {
  filters: TransactionFilters;
  onFiltersChange: (filters: TransactionFilters) => void;
  transactionTypes: string[];
}

export default function UserTransactionFilters({
  filters,
  onFiltersChange,
  transactionTypes,
}: UserTransactionFiltersProps) {
  const instanceId = Math.random().toString(36).substring(2, 9);
  const [isFilterOpen, setIsFilterOpen] = useState(false);
  const [localFilters, setLocalFilters] = useState<TransactionFilters>(filters);

  // Count active filters (excluding "all_types" type)
  const activeFilterCount = [
    localFilters.type !== "all_types" ? 1 : 0,
    localFilters.startDate ? 1 : 0,
    localFilters.endDate && localFilters.startDate ? 1 : 0,
  ].reduce((a, b) => a + b, 0);

  // Apply filters
  const handleApplyFilters = () => {
    onFiltersChange(localFilters);
    setIsFilterOpen(false);
  };

  // Reset filters
  const handleResetFilters = () => {
    const resetFilters = {
      type: "all_types",
      startDate: undefined,
      endDate: undefined,
    };
    setLocalFilters(resetFilters);
    onFiltersChange(resetFilters);
  };

  return (
    <Popover open={isFilterOpen} onOpenChange={setIsFilterOpen}>
      <PopoverTrigger asChild>
        <Button
          variant={activeFilterCount > 0 ? "default" : "outline"}
          size="sm"
          className="flex items-center gap-2"
        >
          <Filter className="h-4 w-4" />
          <span>Filter</span>
          {activeFilterCount > 0 && (
            <Badge
              variant="secondary"
              className="ml-1 rounded-full px-1 py-0 text-xs"
            >
              {activeFilterCount}
            </Badge>
          )}
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-80 p-0" sideOffset={5}>
        <div className="flex flex-col max-h-[80vh]">
          {/* Header */}
          <div className="p-4 border-b">
            <h3 className="font-medium">Filter Transactions</h3>
          </div>

          {/* Scrollable content */}
          <div className="p-4 space-y-4 overflow-y-auto">
            <div className="space-y-2">
              <label className="text-sm font-medium">Transaction Type</label>
              <Select
                value={localFilters.type}
                onValueChange={(value) =>
                  setLocalFilters({ ...localFilters, type: value })
                }
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_types">All Types</SelectItem>
                  {transactionTypes.map((type, index) => {
                    const uniqueKey = `user-transaction-filter-item-${instanceId}-${index}`;
                    return (
                      <SelectItem key={uniqueKey} value={type}>
                        {type}
                      </SelectItem>
                    );
                  })}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">Start Date</label>
              <DatePicker
                date={localFilters.startDate}
                setDate={(date) =>
                  setLocalFilters({ ...localFilters, startDate: date })
                }
                placeholder="Select start date"
              />
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">End Date</label>
              <DatePicker
                date={localFilters.endDate}
                setDate={(date) =>
                  setLocalFilters({ ...localFilters, endDate: date })
                }
                placeholder="Select end date"
                disabled={!localFilters.startDate}
              />
            </div>
          </div>

          {/* Fixed footer with buttons */}
          <div className="p-4 border-t mt-auto flex justify-between">
            <Button variant="outline" size="sm" onClick={handleResetFilters}>
              Reset
            </Button>
            <Button size="sm" onClick={handleApplyFilters}>
              Apply
            </Button>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
}
