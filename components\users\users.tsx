/* eslint-disable @typescript-eslint/no-unused-vars */
// src/components/users/UsersClientPage.tsx

"use client";

import React, { useMemo, useTransition, useEffect, useState } from "react";
import { useBranch } from "@/store/branch-provider";
import dynamic from "next/dynamic";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { parseAsInteger, useQueryState, parseAsString } from "nuqs";
import { useQuery } from "@tanstack/react-query";
import { fetchUsers } from "@/lib/fetchUsers";
import type {
  User,
  FetchUsersParams,
  GlobalDistributionApiResponse,
  CountryListItem,
} from "@/types/user";
import type { UserStatsData } from "@/types/stats";
import { toast } from "sonner";
import { UserHeader } from "./UserHeader";
import { UserTable } from "./UserTable";
import { UserPagination } from "./UserPagination";
import { fetchGlobalDistribution } from "@/lib/fetchGlobalDistribution";
import { fetchUserStats } from "@/lib/fetchUserStats";
import { exportUsers } from "@/lib/exportUsers";
import { countryNameToCodeMap } from "@/lib/utils";
import Image from "next/image";

const GlobalDistributionMap = dynamic(
  () => import("@/components/map/GlobalDistributionMap"),
  {
    ssr: false,
    loading: () => <Skeleton className="h-[350px] w-full rounded-md" />,
  }
);

interface UsersClientPageProps {
  initialSearch?: string;
}

export default function UsersClientPage({
  initialSearch = "",
}: UsersClientPageProps) {
  const [, startUrlUpdateTransition] = useTransition();
  const itemsPerPage = 10;
  const [visibleCountries, setVisibleCountries] = useState(3);
  const { selectedBranchId } = useBranch();
  const [isClient, setIsClient] = useState(false);
  const [isBranchFilterLoading, setIsBranchFilterLoading] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  const [page, setPage] = useQueryState(
    "page",
    parseAsInteger.withDefault(1).withOptions({
      shallow: false,
      startTransition: startUrlUpdateTransition,
    })
  );
  const [searchQuery, setSearchQuery] = useQueryState("q", {
    defaultValue: initialSearch,
    shallow: false,
    startTransition: startUrlUpdateTransition,
  });
  const [startDate, _setStartDate] = useQueryState(
    "start",
    parseAsString.withOptions({
      shallow: false,
      startTransition: startUrlUpdateTransition,
    })
  );
  const [endDate, _setEndDate] = useQueryState(
    "end",
    parseAsString.withOptions({
      shallow: false,
      startTransition: startUrlUpdateTransition,
    })
  );
  const [locationId, _setLocationId] = useQueryState(
    "location_id",
    parseAsInteger.withOptions({
      shallow: false,
      startTransition: startUrlUpdateTransition,
    })
  );
  const [sortBy, setSortBy] = useQueryState("sort", {
    defaultValue: "Date",
    shallow: false,
    startTransition: startUrlUpdateTransition,
  });

  const apiFetchParams: FetchUsersParams = useMemo(
    () => ({
      start: startDate || undefined,
      end: endDate || undefined,
      location_id:
        selectedBranchId !== null ? selectedBranchId : locationId ?? undefined,
    }),
    [startDate, endDate, locationId, selectedBranchId]
  );

  const userListQueryKey = ["users", apiFetchParams];

  const {
    data: userQueryResult,
    isLoading: isUserQueryLoading,
    error: userQueryError,
    isError: isUserQueryError,
  } = useQuery<{ users: User[] }>({
    queryKey: userListQueryKey,
    queryFn: () => fetchUsers(apiFetchParams),
    placeholderData: (previousData) => previousData,
  });

  const {
    data: globalDistData,
    isLoading: isGlobalDistLoading,
    error: globalDistError,
    isError: isGlobalDistError,
  } = useQuery<GlobalDistributionApiResponse>({
    queryKey: ["globalDistribution"],
    queryFn: () => fetchGlobalDistribution(),
  });

  const {
    data: userStatsData,
    isLoading: isUserStatsLoading,
    error: userStatsError,
    isError: isUserStatsError,
  } = useQuery<UserStatsData>({
    queryKey: ["userStats"],
    queryFn: () => fetchUserStats(),
  });

  useEffect(() => {
    setIsBranchFilterLoading(selectedBranchId !== null && isUserQueryLoading);
    // Reset visible countries count when branch changes
    setVisibleCountries(3);
  }, [selectedBranchId, isUserQueryLoading]);

  useEffect(() => {
    if (userQueryError) {
      toast.error(`Error loading users: ${(userQueryError as Error).message}`);
    }
    if (globalDistError) {
      toast.error(
        `Error loading global stats: ${(globalDistError as Error).message}`
      );
    }
    if (userStatsError) {
      // Add error handling for user stats
      toast.error(
        `Error loading user stats: ${(userStatsError as Error).message}`
      );
    }
  }, [userQueryError, globalDistError, userStatsError]);

  const allUsers = useMemo(
    () => userQueryResult?.users ?? [],
    [userQueryResult?.users]
  );

  const filteredUsers = useMemo(() => {
    return allUsers.filter((user) => {
      const lowerQuery = searchQuery?.toLowerCase() ?? "";
      if (!lowerQuery) return true;
      return (
        user.name.toLowerCase().includes(lowerQuery) ||
        user.email.toLowerCase().includes(lowerQuery) ||
        user.phone.includes(lowerQuery) ||
        user.location.city.toLowerCase().includes(lowerQuery) ||
        user.location.state.toLowerCase().includes(lowerQuery) ||
        user.location.address.toLowerCase().includes(lowerQuery) ||
        user.location.branch.toLowerCase().includes(lowerQuery) ||
        user.location.country.toLowerCase().includes(lowerQuery)
      );
    });
  }, [allUsers, searchQuery]);

  const sortedUsers = useMemo(() => {
    const usersToSort = [...filteredUsers];
    if (sortBy === "Date") {
      return usersToSort.sort(
        (a, b) => b.registrationTimestamp - a.registrationTimestamp
      );
    } else if (sortBy === "Name") {
      return usersToSort.sort((a, b) => a.name.localeCompare(b.name));
    }
    return usersToSort;
  }, [filteredUsers, sortBy]);

  const totalPages = Math.ceil(sortedUsers.length / itemsPerPage);
  const currentPageItems = useMemo(() => {
    const adjustedPage = Math.max(1, Math.min(page ?? 1, totalPages || 1));
    if ((page ?? 1) !== adjustedPage && totalPages > 0) {
      setTimeout(() => setPage(adjustedPage), 0);
    }
    const startIndex = (adjustedPage - 1) * itemsPerPage;
    return sortedUsers.slice(startIndex, startIndex + itemsPerPage);
  }, [sortedUsers, page, itemsPerPage, totalPages, setPage]);

  const countryListItems: CountryListItem[] = useMemo(() => {
    if (!globalDistData?.data) return [];

    // If a branch is selected, filter to only show countries with users
    if (selectedBranchId) {
      // Get the filtered users for the selected branch
      const filteredUsers = userQueryResult?.users || [];

      // If there are no users, return empty array
      if (filteredUsers.length === 0) return [];

      // Get unique countries from the filtered users
      const countryMap = new Map<
        string,
        { count: number; name: string; id: string }
      >();

      filteredUsers.forEach((user) => {
        const country = user.location.country || "Unknown";
        const countryKey = country.toUpperCase();
        // Try to get the country code, first from the uppercase key, then from lowercase, fallback to NG
        const countryCode =
          countryNameToCodeMap[countryKey] ||
          countryNameToCodeMap[country.toLowerCase()] ||
          "NG";

        if (!countryMap.has(countryKey)) {
          countryMap.set(countryKey, {
            count: 0,
            name: country,
            id: countryCode,
          });
        }

        countryMap.get(countryKey)!.count++;
      });

      // Convert to array and calculate percentages
      const totalUsers = filteredUsers.length;
      return Array.from(countryMap.entries())
        .map(([_, data]) => ({
          id: data.id,
          name: data.name,
          percentage: Math.round((data.count / totalUsers) * 100),
        }))
        .sort((a, b) => b.percentage - a.percentage);
    }

    // Default behavior for global view (no branch selected)
    const countriesWithUsers = globalDistData.data.filter((d) => d.users > 0);

    // If no countries have users, return empty array
    if (countriesWithUsers.length === 0) {
      return [];
    }

    return countriesWithUsers.map((d) => ({
      id: d.id,
      name: d.country,
      percentage: d.percentage,
    }));
  }, [globalDistData, selectedBranchId, userQueryResult?.users]);

  const handleLoadMoreCountries = () => {
    setVisibleCountries((prev) => Math.min(prev + 3, countryListItems.length));
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value || null);
    setPage(1);
  };
  const handleSortChange = (value: string) => {
    setSortBy(value);
  };
  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };
  const handleActionClick = (userId: number) => {
    toast(`Action clicked for user ID: ${userId}`);
  };

  // Add state for tracking export loading
  const [isExporting, setIsExporting] = useState(false);

  const handleExport = async () => {
    try {
      setIsExporting(true);
      const blob = await exportUsers();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = url;
      link.setAttribute("download", "users.csv");
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
      toast.success("Users exported successfully");
    } catch (error) {
      toast.error("Failed to export users");
    } finally {
      setIsExporting(false);
    }
  };

  return (
    <main className="container mx-auto px-4 py-6 md:px-6 space-y-6">
      {/* --- Global Distribution Section --- */}
      <Card className="overflow-y-auto">
        <CardHeader>
          <CardTitle>GLOBAL</CardTitle>
        </CardHeader>
        <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4 md:gap-6">
          {/* Map Column */}
          <div className="md:col-span-2">
            {/* Pass fetched data and status directly to the map */}
            <GlobalDistributionMap
              initialData={globalDistData?.data}
              isLoading={isGlobalDistLoading || isBranchFilterLoading}
              isError={isGlobalDistError}
              error={globalDistError}
              totalUsers={userQueryResult?.users?.length || 0}
              activeUserPercentage={globalDistData?.activeUserPercentage}
            />
          </div>
          {/* Stats & Country List Column */}
          <div className="md:col-span-1 space-y-4">
            {!isClient || isGlobalDistLoading || isBranchFilterLoading ? (
              <>
                <Skeleton className="h-10 w-3/4" />
                <Skeleton className="h-5 w-1/2 mb-4" />
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-8 w-full mb-2" />
                <Skeleton className="h-8 w-full mb-2" />
              </>
            ) : isGlobalDistError ? (
              <div className="p-4 text-center text-destructive bg-destructive/10 rounded-md">
                Could not load global stats.
              </div>
            ) : globalDistData ? (
              <>
                {/* Total Users & Active % */}
                <div>
                  <p className="text-3xl tracking-tight">
                    {(selectedBranchId && userQueryResult?.users
                      ? userQueryResult.users.length
                      : globalDistData?.totalUsers || 0
                    ).toLocaleString()}
                  </p>
                  <div className="flex items-center">
                    <p className="text-sm font-medium flex items-center">
                      {/* Determine arrow direction based on active user percentage */}
                      {globalDistData.activeUserPercentage >= 50 ? (
                        <span className="text-green-600 flex items-center">
                          <span className="mr-1">↗︎</span>
                          {globalDistData.activeUserPercentage}%
                        </span>
                      ) : globalDistData.activeUserPercentage >= 25 ? (
                        <span className="text-yellow-600 flex items-center">
                          <span className="mr-1">→</span>
                          {globalDistData.activeUserPercentage}%
                        </span>
                      ) : (
                        <span className="text-red-600 flex items-center">
                          <span className="mr-1">↘︎</span>
                          {globalDistData.activeUserPercentage}%
                        </span>
                      )}
                      <span className="text-[#888887] ml-1">active users</span>
                    </p>
                    {/* Info tooltip */}
                    <div className="relative ml-1 group">
                      <span className="cursor-help text-muted-foreground text-xs">
                        ⓘ
                      </span>
                      <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-2 bg-black text-white text-xs rounded w-48 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity">
                        Active users are those with at least one wallet
                        containing a non-zero balance.
                      </div>
                    </div>
                  </div>
                </div>

                {/* Country List */}
                {countryListItems.length > 0 ? (
                  <>
                    <div className="space-y-3 pt-2 overflow-y-auto">
                      {countryListItems
                        .slice(0, visibleCountries)
                        .map((item) => {
                          // Construct flag path dynamically
                          const flagPath = `/flags/3x2/${item.id.toUpperCase()}.svg`;
                          return (
                            <div key={item.id}>
                              <div className="flex items-center w-full gap-2">
                                {/* Country flag and name */}
                                <div className="flex items-center gap-2">
                                  <div className="w-8 h-8 rounded-full border border-gray-200 flex items-center justify-center overflow-hidden bg-gray-50">
                                    <Image
                                      src={flagPath}
                                      alt={`${item.name} flag`}
                                      width={48}
                                      height={48}
                                      className="w-full h-full object-cover"
                                      onError={(e) => {
                                        // If flag fails to load, show the first letter of country name
                                        e.currentTarget.style.display = "none";
                                        e.currentTarget.parentElement!.innerHTML = `<span class="text-sm font-bold">${item.name.charAt(
                                          0
                                        )}</span>`;
                                      }}
                                    />
                                  </div>
                                </div>

                                {/* Progress bar */}
                                <div className="h-1.5 bg-violet-200 rounded-full flex-1 mx-1 relative">
                                  <div
                                    className="h-full bg-violet-500 rounded-full"
                                    style={{ width: `${item.percentage}%` }}
                                  />

                                  <span className="absolute -top-5 font-medium text-sm uppercase">
                                    {item.name}
                                  </span>
                                </div>

                                {/* Percentage */}
                                <span className="font-bold text-sm">
                                  {item.percentage}%
                                </span>
                              </div>
                            </div>
                          );
                        })}
                    </div>

                    {/* Load More Button */}
                    {countryListItems.length > visibleCountries && (
                      <Button
                        variant="link"
                        className="p-0 h-auto text-sm text-primary"
                        onClick={handleLoadMoreCountries}
                      >
                        Load more
                      </Button>
                    )}
                  </>
                ) : (
                  <p className="text-sm text-muted-foreground text-center py-4">
                    {selectedBranchId
                      ? "No users found for the selected branch."
                      : "No country data available."}
                  </p>
                )}
              </>
            ) : null}
          </div>
        </CardContent>
      </Card>

      {/* --- User List Section --- */}
      <Card className="overflow-hidden">
        <div className="p-6">
          <UserHeader
            userCount={
              !isClient || isUserQueryLoading || isBranchFilterLoading
                ? "..."
                : isUserQueryError
                ? "Error"
                : userQueryResult?.users?.length.toLocaleString() ?? "0"
            }
            searchQuery={searchQuery ?? ""}
            sortBy={sortBy ?? "Date"}
            onSearchChange={handleSearch}
            onSortChange={handleSortChange}
            onExportClick={handleExport}
            isExportLoading={isExporting}
          />

          <UserTable
            users={currentPageItems}
            isLoading={!isClient || isUserQueryLoading || isBranchFilterLoading}
            isError={isUserQueryError}
            itemsPerPage={itemsPerPage}
            onActionClick={handleActionClick}
          />

          {isClient &&
            !isUserQueryLoading &&
            !isBranchFilterLoading &&
            !isUserQueryError &&
            totalPages > 1 && (
              <UserPagination
                currentPage={page ?? 1}
                totalPages={totalPages}
                onPageChange={handlePageChange}
              />
            )}
        </div>
      </Card>
    </main>
  );
}
