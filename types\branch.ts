// types/branch.ts

// Structure for a single branch returned by the GET /admin/branches/all endpoint
export interface Branch {
  id: number;
  name: string;
  address: string;
  city: string;
  state: string;
  country: string;
}

// Structure for the API response when fetching all branches
export interface BranchesApiResponse {
  message: string;
  result: Branch[];
  status: number;
}

export type UpdateBranchPayload = NewBranchPayload;

export type UpdateBranchApiResponse = AddBranchApiResponse;

// Structure for the data needed to create a new branch (POST body)
export interface NewBranchPayload {
  address: string;
  city: string;
  country: string;
  name: string;
  state: string;
}

// Structure for the API response after creating a new branch
export interface AddBranchApiResponse {
  message: string;
  result: string;
  status: number;
}
