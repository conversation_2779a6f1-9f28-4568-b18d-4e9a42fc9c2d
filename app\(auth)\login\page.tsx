"use client";

import type React from "react";

import { useState } from "react";
import { Eye, EyeOff, Mail, Lock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useAuth } from "@/store/auth-provider";
import Logo from "@/components/logo";
import Image from "next/image";

export default function LoginPage() {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [showPassword, setShowPassword] = useState(false);
  const { login, isLoading } = useAuth();

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await login(email, password);
    } catch (err) {
      console.log(
        err instanceof Error ? err.message : "Login failed. Please try again."
      );
    }
  };

  return (
    <div className="container mx-auto px-6 py-6 h-[100dvh]">
      <Logo />
      <div className="mt-12 flex flex-col md:flex-row">
        <div className="bg-[#8F71D4] text-white p-8 md:w-1/2 flex flex-col rounded-2xl">
          <div className="flex-1 flex flex-col justify-center items-center text-center h-auto max-w-sm mx-auto">
            <div className="mb-8">
              <p className="text-lg mb-6">
                I planted, Apollos watered, but God gave the growth. So neither
                he who plants nor he who waters is anything, but only God who
                gives the growth.
              </p>
              <p className="text-lg mb-6">
                He who plants and he who waters are one, and each will receive
                his wages according to his labor. For we are God&apos;s fellow
                workers. You are God&apos;s field, God&apos;s building.
              </p>
              <div className="inline-block bg-[#7b5ebd] px-6 py-2 rounded-full">
                1 Corinthians 3:6-9 ESV
              </div>
            </div>

            <div className="bg-white text-black p-6 rounded-xl w-full max-w-sm">
              <div className="flex items-center mb-4">
                <div className="bg-[#f1f9f1] p-2 rounded-md mr-3">
                  <svg
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"
                      stroke="#51ac32"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                    <path
                      d="M12 3a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"
                      stroke="#51ac32"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                </div>
                <h3 className="font-bold uppercase">Total Users</h3>
                <div className="ml-auto">
                  <div className="w-16 h-auto">
                    <Image
                      src="/flag.svg"
                      alt="Nigeria Flags"
                      width={200}
                      height={150}
                      className="w-full h-auto"
                      priority
                    />
                  </div>
                </div>
              </div>

              <h2 className="text-4xl font-bold mb-2">005,000</h2>
              <p className="text-[#51ac32] flex items-center">
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                  className="mr-1"
                >
                  <path
                    d="M7 17l5-5 5 5"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                  <path
                    d="M7 7h10"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
                10% than last month
              </p>
            </div>
          </div>
        </div>

        {/* Right Section - Login Form */}
        <div className="bg-white p-8 md:w-1/2 flex flex-col justify-center">
          <div className="max-w-md mx-auto w-full">
            <h1 className="text-4xl font-bold mb-2">WELCOME BACK</h1>
            <p className="text-gray-500 mb-8">Continue with email address</p>

            <form onSubmit={handleLogin}>
              <div className="mb-4 relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <Mail size={20} />
                </div>
                <Input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="pl-10 bg-gray-100 border-0 rounded-full"
                  placeholder="Email address"
                  required
                />
              </div>

              <div className="mb-2 relative">
                <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400">
                  <Lock size={20} />
                </div>
                <Input
                  type={showPassword ? "text" : "password"}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="pl-10 bg-gray-100 border-0 rounded-full"
                  placeholder="Password"
                  required
                />
                <button
                  type="button"
                  className="rounded-full absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  onClick={() => setShowPassword(!showPassword)}
                >
                  {showPassword ? <EyeOff size={20} /> : <Eye size={20} />}
                </button>
              </div>

              <div className="flex justify-end mb-6">
                <Link
                  href="/forgot-password"
                  className="text-[#866cef] hover:underline text-sm"
                >
                  Forgot Password?
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full bg-black text-white hover:bg-gray-800 rounded-full"
                disabled={isLoading}
              >
                {isLoading ? "Logging in..." : "Start Tracking"}
              </Button>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
}
