import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export const countryNameToCodeMap: Record<string, string> = {
  // Common countries
  nigeria: "NG",
  "united kingdom": "GB",
  "united states": "US",
  usa: "US", // Handle variations
  canada: "CA",
  "united arab emirates": "AE",

  // Add uppercase versions for direct matching
  NIGERIA: "NG",
  "UNITED KINGDOM": "GB",
  "UNITED STATES": "US",
  USA: "US",
  CANADA: "CA",
  "UNITED ARAB EMIRATES": "AE",

  // Add more countries as needed
  australia: "AU",
  AUSTRALIA: "AU",
  ghana: "GH",
  GHANA: "GH",
  kenya: "KE",
  KENYA: "KE",
  "south africa": "ZA",
  "SOUTH AFRICA": "ZA",

  // Default fallback
  unknown: "NG", // Use Nigeria as fallback instead of XX which doesn't exist
  UNKNOWN: "NG",
};
