// types/manager.ts
import type { Branch } from "./branch";

export interface Manager {
  address: string;
  branches: Branch[];
  country: string;
  email: string;
  first_name: string;
  id: string;
  last_name: string;
  phone: string;
  role: string;
  state: string;
}

export interface UpdateManagerPayload {
  Id?: string;
  first_name?: string;
  last_name?: string;
  email?: string;
  phone?: string;
  address?: string;
  state?: string;
  country?: string;
  role?: string;
  role_name?: string;
  branches?: number[];
}
