import SupportPage from "@/components/support/support";
import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { getQueryClient } from "@/store/get-query-client";
import {
  fetchSupportRequests,
  fetchSupportStatuses,
} from "@/lib/fetchSupportRequests";

export default async function Support() {
  const queryClient = getQueryClient();

  await Promise.all([
    queryClient.prefetchQuery({
      queryKey: ["supportRequests"],
      queryFn: () => fetchSupportRequests(),
    }),
    queryClient.prefetchQuery({
      queryKey: ["supportStatuses"],
      queryFn: () => fetchSupportStatuses(),
    }),
  ]);

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <SupportPage />
    </HydrationBoundary>
  );
}
