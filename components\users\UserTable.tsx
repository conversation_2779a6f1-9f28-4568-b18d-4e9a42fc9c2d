/* eslint-disable @typescript-eslint/no-unused-vars */
import React from "react";
import Link from "next/link";
import { MoreVertical, Search } from "lucide-react";
import { motion, AnimatePresence } from "motion/react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import type { User } from "@/types/user";

// Motion-enabled TableRow for smooth animations
const MotionTableRow = motion.create(TableRow);

interface UserTableProps {
  users: User[];
  isLoading: boolean;
  isError: boolean;
  itemsPerPage: number;
  onActionClick: (userId: number) => void;
}

export function UserTable({
  users,
  isLoading,
  isError,
  itemsPerPage,
  onActionClick,
}: UserTableProps) {
  const renderSkeleton = () => (
    <TableBody>
      {Array.from({ length: itemsPerPage }).map((_, index) => (
        <TableRow key={`skeleton-${index}`}>
          <TableCell>
            <Skeleton className="h-8 w-full" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-full" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-full" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-full" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-full" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-8 w-8 rounded-full" />
          </TableCell>
        </TableRow>
      ))}
    </TableBody>
  );

  const renderError = () => (
    <TableBody>
      <TableRow>
        <TableCell colSpan={6} className="text-center text-red-600 py-8">
          Failed to load users. Please try again later.
        </TableCell>
      </TableRow>
    </TableBody>
  );

  const renderEmpty = () => (
    <TableBody>
      <TableRow>
        <TableCell colSpan={6} className="text-center py-8">
          <div className="flex flex-col items-center justify-center">
            <div className="bg-gray-100 rounded-full p-3 mb-3">
              <Search className="h-6 w-6 text-gray-400" />
            </div>
            <h3 className="text-lg font-medium mb-1">No users found</h3>
            <p className="text-gray-500">
              Try adjusting your search or filters.
            </p>
          </div>
        </TableCell>
      </TableRow>
    </TableBody>
  );

  const renderTableBody = () => (
    <AnimatePresence>
      <TableBody>
        {users.map((user) => (
          <MotionTableRow
            key={user.id}
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            layout
          >
            <TableCell>
              <div>
                <div className="text-sm font-medium">
                  <Link
                    href={`/users/${user.id}`}
                    className="hover:text-[#866cef]"
                  >
                    {user.name}
                  </Link>
                </div>
                <div className="text-xs text-gray-500">{user.email}</div>
              </div>
            </TableCell>
            <TableCell className="text-xs">{user.phone}</TableCell>
            <TableCell className="text-xs">{user.partnerId || "N/A"}</TableCell>
            <TableCell>
              <div>
                <div className="text-xs">
                  {user.location.city}, {user.location.state} (
                  {user.location.branch})
                </div>
                <div className="text-xs text-gray-500">
                  {user.location.address}
                </div>
              </div>
            </TableCell>
            <TableCell className="text-xs">{user.registeredOn}</TableCell>
            <TableCell>
              {/* TODO: Add dropdown or actions */}
              <Link href={`/users/${user.id}`}>
                <Button variant="ghost" size="icon" className="h-8 w-8">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </Link>
            </TableCell>
          </MotionTableRow>
        ))}
      </TableBody>
    </AnimatePresence>
  );

  return (
    <div className="overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="text-xs font-medium text-gray-500">
              NAME / EMAIL
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              PHONE NO.
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              PARTNER ID
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              LOCATION (BRANCH)
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500">
              REGISTERED ON
            </TableHead>
            <TableHead className="text-xs font-medium text-gray-500" />
          </TableRow>
        </TableHeader>
        {isLoading
          ? renderSkeleton()
          : isError
          ? renderError()
          : users.length > 0
          ? renderTableBody()
          : renderEmpty()}
      </Table>
    </div>
  );
}
