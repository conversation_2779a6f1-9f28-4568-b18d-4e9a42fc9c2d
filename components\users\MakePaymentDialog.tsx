"use client";

import { useState, useEffect } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  makeOfflinePayment,
  type OfflinePaymentPayload,
} from "@/lib/makeOfflinePayment";
import { useAuth } from "@/store/auth-provider";
import { useBranch } from "@/store/branch-provider";
import type { User } from "@/types/user";

interface MakePaymentDialogProps {
  isOpen: boolean;
  onClose: () => void;
  user: User;
}

const paymentTypes = [
  { value: "Offering", label: "Offering" },
  { value: "Tithe", label: "Tithe" },
  { value: "Partnership", label: "Partnership" },
  { value: "First-fruit", label: "First Fruit" },
  { value: "Seed", label: "Seed" },
  { value: "Prophet Offering", label: "Prophet Offering" },
];

const currencies = [
  { value: "NGN", label: "NGN" },
  { value: "USD", label: "USD" },
  { value: "GBP", label: "GBP" },
  { value: "EUR", label: "EUR" },
  { value: "CAD", label: "CAD" },
];

export default function MakePaymentDialog({
  isOpen,
  onClose,
  user,
}: MakePaymentDialogProps) {
  const { user: authUser } = useAuth();
  const { selectedBranchId } = useBranch();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    amount: "",
    currency: "NGN",
    payment_type: "",
    payment_reference: "",
    payment_date: "",
  });

  // Reset form when dialog opens
  useEffect(() => {
    if (isOpen) {
      const today = new Date().toISOString().split("T")[0];
      setFormData({
        amount: "",
        currency: "NGN",
        payment_type: "",
        payment_reference: "",
        payment_date: today,
      });
    }
  }, [isOpen]);

  const { mutate: submitPayment, isPending: isSubmitting } = useMutation({
    mutationFn: makeOfflinePayment,
    onSuccess: (data) => {
      toast.success("Payment processed successfully!");
      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({
        queryKey: ["userWallets", user.id.toString()],
      });
      queryClient.invalidateQueries({
        queryKey: ["userTransactions", user.id.toString()],
      });
      onClose();
    },
    onError: (error) => {
      toast.error(
        error instanceof Error ? error.message : "Failed to process payment"
      );
      console.error("Payment error:", error);
    },
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    // Validation
    if (!formData.amount || parseFloat(formData.amount) <= 0) {
      toast.error("Please enter a valid amount.");
      return;
    }

    if (!formData.payment_type) {
      toast.error("Please select a payment type.");
      return;
    }

    if (!formData.payment_reference.trim()) {
      toast.error("Please enter a payment reference.");
      return;
    }

    if (!formData.payment_date) {
      toast.error("Please select a payment date.");
      return;
    }

    if (!authUser?.email) {
      toast.error("User authentication error. Please try again.");
      return;
    }

    const payload: OfflinePaymentPayload = {
      amount: parseFloat(formData.amount),
      branch_id: selectedBranchId || 0,
      created_by: authUser.email,
      currency: formData.currency,
      member_email: user.email,
      payment_date: formData.payment_date,
      payment_reference: formData.payment_reference.trim(),
      payment_type: formData.payment_type,
    };

    submitPayment(payload);
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md rounded-3xl">
        <DialogHeader>
          <DialogTitle>Make Payment</DialogTitle>
          <DialogDescription>
            Process an offline payment for {user.name} ({user.email})
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="amount" className="text-sm text-gray-500">
                AMOUNT
              </Label>
              <Input
                id="amount"
                type="number"
                step="0.01"
                min="0"
                value={formData.amount}
                onChange={(e) => handleInputChange("amount", e.target.value)}
                placeholder="0.00"
                className="bg-[#f9fafb] border-0 rounded-full"
                required
              />
            </div>

            <div>
              <Label htmlFor="currency" className="text-sm text-gray-500">
                CURRENCY
              </Label>
              <Select
                value={formData.currency}
                onValueChange={(value) => handleInputChange("currency", value)}
              >
                <SelectTrigger className="bg-[#f9fafb] border-0 rounded-full">
                  <SelectValue placeholder="Select currency" />
                </SelectTrigger>
                <SelectContent>
                  {currencies.map((currency) => (
                    <SelectItem key={currency.value} value={currency.value}>
                      {currency.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="payment_type" className="text-sm text-gray-500">
              PAYMENT TYPE
            </Label>
            <Select
              value={formData.payment_type}
              onValueChange={(value) =>
                handleInputChange("payment_type", value)
              }
            >
              <SelectTrigger className="bg-[#f9fafb] border-0 rounded-full">
                <SelectValue placeholder="Select payment type" />
              </SelectTrigger>
              <SelectContent>
                {paymentTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label
              htmlFor="payment_reference"
              className="text-sm text-gray-500"
            >
              PAYMENT REFERENCE
            </Label>
            <Input
              id="payment_reference"
              type="text"
              value={formData.payment_reference}
              onChange={(e) =>
                handleInputChange("payment_reference", e.target.value)
              }
              placeholder="Enter payment reference"
              className="bg-[#f9fafb] border-0 rounded-full"
              required
            />
          </div>

          <div>
            <Label htmlFor="payment_date" className="text-sm text-gray-500">
              PAYMENT DATE
            </Label>
            <Input
              id="payment_date"
              type="date"
              value={formData.payment_date}
              onChange={(e) =>
                handleInputChange("payment_date", e.target.value)
              }
              className="bg-[#f9fafb] border-0 rounded-full"
              required
            />
          </div>

          <DialogFooter className="gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
              className="rounded-full"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className="bg-black hover:bg-gray-800 text-white rounded-full"
            >
              {isSubmitting ? "Processing..." : "Process Payment"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
