import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function DeclarationCardSkeleton() {
  return (
    <Card className="relative h-[300px] overflow-hidden p-6 flex flex-col">
      {/* Skeleton for Edit Button */}
      <Skeleton className="absolute top-2 left-2 h-8 w-8 rounded-full bg-gray-200" />

      {/* Skeleton for Text Content */}
      <div className="flex-1 flex items-center">
        <div className="space-y-2 w-full">
          <Skeleton className="h-4 w-3/4 bg-gray-200" />
          <Skeleton className="h-4 w-1/2 bg-gray-200" />
          <Skeleton className="h-4 w-5/6 bg-gray-200" />
        </div>
      </div>

      {/* Skeleton for Date and Remove Button */}
      <div className="mt-auto space-y-3">
        <Skeleton className="h-3 w-1/3 bg-gray-200 mb-4" />
        <Skeleton className="h-8 w-20 rounded-full bg-gray-200" />
      </div>
    </Card>
  );
}
