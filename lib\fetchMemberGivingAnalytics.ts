"use server";

import { cookies } from "next/headers";

// Define the structure for the API response
export interface MemberGivingAnalyticsApiResponse {
  message: string;
  result: MemberGivingAnalyticsItem[] | null;
  status: number;
}

export interface MemberGivingAnalyticsItem {
  Month: string;
  Total: string;
  HighestAmount: string;
}
export interface FetchMemberGivingAnalyticsParams {
  memberId: string;
  currency?: string;
  payment_type?: string;
  period?: "day" | "week" | "month" | "year";
}

export interface GrowthChartDataPoint {
  date: string;
  value: number;
}

export const fetchMemberGivingAnalytics = async ({
  memberId,
  currency = "NGN",
  payment_type,
  period,
}: FetchMemberGivingAnalyticsParams): Promise<GrowthChartDataPoint[]> => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;

  if (!memberId) {
    throw new Error("Missing required parameter: memberId");
  }

  // Build query parameters
  const queryParams = new URLSearchParams();
  if (currency) queryParams.append("currency", currency);
  if (payment_type) queryParams.append("payment_type", payment_type);
  if (period) queryParams.append("period", period);

  // Construct the API URL
  const BASE_URL = process.env.BASE_URL;
  const API_URL = `${BASE_URL}/admin/members/${memberId}/giving-analytics?${queryParams.toString()}`;

  try {
    const response = await fetch(API_URL, {
      headers: {
        Authorization: `Bearer ${token}`,
        accept: "application/json",
      },
      cache: "no-store",
    });

    if (!response.ok) {
      let errorBody = "Unknown error";
      try {
        const errorData = await response.json();
        errorBody = errorData.message || JSON.stringify(errorData);
      } catch {
        // Ignore if error body isn't valid JSON
      }
      throw new Error(
        `API request failed with status ${response.status}: ${errorBody}`
      );
    }

    const data: MemberGivingAnalyticsApiResponse = await response.json();

    // Check the status within the response body
    if (data.status !== 200) {
      throw new Error(
        `API returned an error status ${data.status}: ${data.message}`
      );
    }

    try {
      if (
        !data.result ||
        !Array.isArray(data.result) ||
        data.result.length === 0
      ) {
        console.log(
          `No giving analytics data found for member ${memberId}, currency: ${currency}, period: ${
            period || "default"
          }`
        );
        return [];
      }

      const chartData: GrowthChartDataPoint[] = data.result.map((item) => {
        // Parse the Total value as a number
        const totalValue = parseInt(item.Total || "0", 10);

        // Use the Month directly from the API response
        const formattedDate = item.Month || "Unknown";

        return {
          date: formattedDate,
          value: totalValue,
        };
      });

      return chartData;
    } catch (transformError) {
      console.error(
        "Error transforming giving analytics data:",
        transformError
      );
      return []; // Return empty array on transformation error
    }
  } catch (error) {
    console.error("Error fetching member giving analytics:", error);
    // Re-throw the error to be handled by the calling code (e.g., react-query)
    throw error;
  }
};
